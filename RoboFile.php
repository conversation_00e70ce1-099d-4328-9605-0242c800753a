<?php

require_once 'vendor/autoload.php';

/**
 * This is project's console commands configuration for Robo task runner.
 *
 * @see https://robo.li/
 */
class RoboFile extends \Robo\Tasks
{
    use Codeception\Task\Merger\ReportMerger;
    use Codeception\Task\Splitter\TestsSplitterTrait;

    public function fullpassStaging()
    {
        $this->taskCodecept()
            ->suite('Acceptance')
            ->env('staging,chrome')
            ->group('paracept_1') # b2b
            ->group('paracept_2') # b2c
            ->group('paracept_3') # DataTools
            ->group('paracept_4') # general
            ->html('reports/fullpass_staging.html')
            ->run();
    }

    public function fullpassProd()
    {
        $this->taskCodecept()
            ->suite('Acceptance')
            ->env('prod,chrome')
            ->group('paracept_1') # b2b
            ->group('paracept_2') # b2c
            ->group('paracept_3') # DataPrep
            ->group('paracept_4') # general
            ->html('reports/fullpass_prod.html')
            ->run();
    }

    public function sanityStaging()
    {
        $this->taskCodecept()
            ->suite('Acceptance')
            ->env('staging,chrome')
            ->group('sanity')
            ->html('reports/sanity_staging.html')
            ->run();
    }

    public function sanityProd()
    {
        $this->taskCodecept()
            ->suite('Acceptance')
            ->env('prod,chrome')
            ->group('sanity')
            ->html('reports/sanity_prod.html')
            ->run();
    }

    public function complianceOptout()
    {
        $this->taskCodecept()
            ->suite('Acceptance')
            ->env('compliance-optout-prod,chrome')
            ->group('optout')
            ->html('reports/compliance_optout_prod.html')
            ->run();
    }

    public function complianceCCPA()
    {
        $this->taskCodecept()
            ->suite('Acceptance')
            ->env('prod,chrome')
            ->group('ccpa')
            ->html('reports/compliance_ccpa_prod.html')
            ->run();
    }

    // robo customer:report-stg
    public function customerReportStg()
    {
        $this->taskCodecept()
            ->suite('Acceptance')
            ->env('customer-report-staging')
            ->group('customer')
            ->html('reports/customer-report-staging.html')
            ->run();
    }

    public function customerReportProd()
    {
        $this->taskCodecept()
            ->suite('Acceptance')
            ->env('customer-report-prod')
            ->group('customer')
            ->html('reports/customer-report-prod.html')
            ->run();
    }

    // Run the customer test suite in staging & prod
    // (this technically works, but the 2 browser instances creates a lot of test flakiness, bummer)
    public function parallelCustomerReport()
    {
        $parallel = $this->taskParallelExec();
        $parallel->process(
            $this->taskCodecept()
                ->suite('Acceptance')
                ->env('customer-report-staging')
                ->group('customer')
                ->html('reports/customer-report-staging.html')
        );
        $parallel->process(
            $this->taskCodecept()
                ->suite('Acceptance')
                ->env('customer-report-prod')
                ->group('customer')
                ->html('reports/customer-report-prod.html')
        );
        return $parallel->run();
    }

    // cmd not working, but manually creating paracept_ files works
    // public function parallelSplitTests()
    // {
    //     $this->taskSplitTestFilesByGroups(5)
    //         ->projectRoot('.')
    //         ->testsFrom('tests/acceptance/b2b')
    //         ->groupsTo('tests/Support/Data/paracept_')
    //         ->run();
    // }

    // Example of running parallel tests
    public function parallelTest()
    {
        $parallel = $this->taskParallelExec();

        $parallel->process(
            $this->taskCodecept()  # use built-in Codecept task
            ->suite('Acceptance')
            ->env('staging,chrome')
            ->group("paracept_1")
            ->html('reports/report_1.html')
        );

        return $parallel->run();
    }

    public function parallelMergeResults()
    {

    }
}