# REACH Automated Testing

This project uses the PHP testing framework Codeception to perform Selenium-based UI tests on [Versium REACH](https://app.versium.com/). This project can also be used to run SQL query reports against Kittiverse/Converse.

## Getting Started

Prerequisites

- Brew install PHP >= 8.1
- <PERSON>rew install selenium-server
- <PERSON>rew install chromedriver (required for Chrome)
- Brew install geckodriver (optional, required for Firefox)

Setup

- `git clone` this repository
- From the root, install codeception and its dependencies: `composer install`
- If this is the first time running the tests, you will need to generate the codeception helper files that will exist in ./tests/Support/_generated. This only needs to be done once: `./vendor/bin/codecept build`

- Set the environment variable VERSIUM_APP_TEST_EMAIL and VERSIUM_APP_TEST_PWD to the username and password of the Reach test account that will be logging into the app:

```
export VERSIUM_APP_TEST_EMAIL='<EMAIL>'
export VERSIUM_APP_TEST_PWD='password'
```

Running Reach UI Tests
- Start the selenium server: `selenium-server standalone`
- To run a full test pass on Reach b2b features on staging:

```
./vendor/bin/codecept run tests/Acceptance/b2b --env staging,chrome
```


- To run a sanity pass on Reach b2c features on production:

```
./vendor/bin/codecept run tests/Acceptance/b2c --env prod,chrome --group sanity
```

- Check the configuration file in `tests/Acceptance.suite.yml` and `tests/_envs` to see the available envs. Envs can be combined with a comma and merged liked so: `--env dev,safari`

## Setting Up a Local Test Env

Running a Selenium-powered browser instance commonly and frustratingly interferes with other tasks you may want to do on your machine while the automation is running. If you are using MacOS you can follow the instructions below to create an isolated local test environment for the browser to run in. (Assumes MacOS >= 12.14 )

1. Create a new user account on your system named something like "TestAutomation"
2. In the new account navigate to System Preferences > Sharing and enable both "screen sharing" and "remote login"
3. In your main user account create an ssh tunnel to the new TestAutomation account, forwarding the VNC port:
```
ssh -NL 5901:localhost:5900 TestAutomation@localhost
```
4. Launch the MacOS app Screen Sharing
5. When Screen Sharing shows the prompt "Connect to" enter: `localhost:5901`
6. Enter the username and password for TestAutomation
7. When prompted how you would like to connect, choose "Login as yourself"
8. In the remotely viewed account, open a terminal and start the selenium server: `selenium-server standalone`
9. Now when you execute a codeception test suite in your main user account, the automated browser instance will launch in the TestAutomation account, creating an isolated local environment that you can view and interact with via the Screen Sharing app.
