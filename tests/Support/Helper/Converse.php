<?php

namespace Tests\Support\Helper;

define ('RECORD_COUNT','recordCount');
define ('PHONES', 'phones');

// here you can define custom actions
// all public methods declared in helper class will be available in $I

class Converse extends \Codeception\Module
{
    protected array $config = [
        'server' => '127.0.0.1:3307',
        'username' => 'root',
        'password' => '',
        'dbname' => 'StarniumHoldings'
    ];

    /**
     * Define custom actions here
     */
    public function getServer()
    {
        return $this->config['server'];
    }

    public function getUserName()
    {
        return $this->config['username'];
    }

    public function getPassword()
    {
        return $this->config['password'];
    }

    public function getDBName()
    {
        return $this->config['dbname'];
    }
}
