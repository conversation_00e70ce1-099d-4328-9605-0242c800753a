<?php

namespace Tests\Support\Helper;

define ('TOTAL_RECORD_COUNT','totalRecordCount');
define ('UNIQUE_DOMAIN_KEY_COUNT','uniqueDomainKeyCount');
define ('UNIQUE_CORP_DOMAIN_COUNT','uniqueCorpDomainCount');
define ('UNIQUE_CORP_NAME_COUNT','uniqueCorpNameCount');
define ('UNIQUE_EMAIL_COUNT','uniqueEmailCount');
define ('UNIQUE_SIC_COUNT','uniqueSICCount');
define ('TOP_DOMAINS','topDomains');
define ('TOP_COMPANIES','topCompanies');
define ('SIC_DISTRIBUTION','SICDistribution');
define ('LAST_NAME_FIRST_NAME_MATCH_COUNT','lastNameFirstNameMatchCount');
define ('BAD_CHAR_IN_EMAIL_RECORD_COUNT','badCharacterInEmailRecordCount');
define ('DOMAINS_WITHOUT_NUMEMPLOYEES_COUNT','domainsWithoutNumEmployeesCount');
define ('DOMAINS_WITHOUT_SALES_COUNT','domainsWithoutSalesCount');
define ('DOMAINS_WITH_TICKER_COUNT','domainsWithTickerCount');
define ('RECORDS_WITH_TICKER_COUNT','recordsWithTickerCount');

define ('PINDIV_MATCHES', 'pindivMatches'); // new!
define ('DOMAINS_GREATER_THAN_10K', 'countOfDomainsGreaterThan10k'); // new!
define ('CORP_NAMES_GREATER_THAN_10K', 'countOfCorpNamesGreaterThan10k'); // new!
define ('EMAIL_GREATER_THAN_200', 'emailAddrGreaterThan200'); // new!

define ('KV_TRUTH_FILE','kvtruth.json');
define ('DOMAINS_OVER_500_FILE', 'domains_with_500_records.csv');
define ('DOMAINS_OVER_500_COUNT_FILE', 'count_domains_with_500_records.csv');
define ('LIUID_FILE', 'liuid.csv');
define ('COLUMNS_COUNT_FILE', 'columns_fill_rate.csv');
define ('KV_COLUMNS_COUNT_FILE', 'KittiversePINDIV-fill-rate.csv');

define ('KV_DATA_DIR', 'kittiverse_data/');

##############################################
// const DB_TABLE = 'KittiversePINDIV';

// here you can define custom actions
// all public methods declared in helper class will be available in $I

class Kittiverse extends \Codeception\Module
{
    protected array $config = [
        'server' => '127.0.0.1:3307',
        'username' => 'root',
        'password' => '',
        'dbname' => 'StarniumHoldings'
    ];

    /**
     * Define custom actions here
     */
    public function getServer()
    {
        return $this->config['server'];
    }

    public function getUserName()
    {
        return $this->config['username'];
    }

    public function getPassword()
    {
        return $this->config['password'];
    }

    public function getDBName()
    {
        return $this->config['dbname'];
    }

}
