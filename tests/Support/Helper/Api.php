<?php

namespace Tests\Support\Helper;

// here you can define custom actions
// all public methods declared in helper class will be available in $I

class Api extends \Codeception\Module
{
    protected array $config = [
        'environment' => ''
    ];

    public function getEnv() {
        return $this->config['environment'];
    }

    public function getAPIProdKey() {
        return getenv('REACH_API_KEY_PROD');
    }

    public function getAPIStagingKey() {
        return getenv('REACH_API_KEY_STAGING');
    }

}
