<?php

namespace Tests\Support\Helper;

// here you can define custom actions
// all public methods declared in helper class will be available in $I

class VersiumDotCom extends \Codeception\Module {

    protected array $config = [
        'email' => '',
        'password' => '',
        'environment' => ''
    ];

    /**
     * Define custom actions here
     */
    public function getVersiumDotComEnv() {
        return $this->config['environment'];
    }

    public function getVersiumDotComStgUsername() {
        return getenv('VERSIUM_DOT_COM_STG_USERNAME');
    }

    public function getVersiumDotComStgPassword() {
        return getenv('VERSIUM_DOT_COM_STG_PASSWORD');
    }
}
