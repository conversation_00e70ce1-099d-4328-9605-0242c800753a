<?php

namespace Tests\Support\Helper;

// here you can define custom actions
// all public methods declared in helper class will be available in $I

class Acceptance extends \Codeception\Module {

    protected array $config = [
        'email' => '',
        'password' => '',
        'environment' => ''
    ];

    /**
     * Define custom actions here
     */
    public function getEmail() {
        return $this->config['email'];
    }

    public function getPassword() {
        return $this->config['password'];
    }

    public function getBrowser() {
        return $this->getModule('WebDriver')->_getConfig('browser');
    }

    public function getConfigUrl() {
        return $this->getModule('WebDriver')->_getConfig('url');
    }

    public function getPasswordResetEmail() {
        return $_ENV['REACH_PASSWORD_RESET_EMAIL_USERNAME'];
    }

    public function getPasswordResetPassword() {
        return $_ENV['REACH_PASSWORD_RESET_EMAIL_PASSWORD'];
    }

    public function getFacebookUsername() {
        return $_ENV['VERSIUM_FACEBOOK_USERNAME'];
    }

    public function getFacebookPassword() {
        return $_ENV['VERSIUM_FACEBOOK_PASSWORD'];
    }

    public function getVersiumGoogleUsername() {
        return $_ENV['VERSIUM_GOOGLE_USERNAME'];
    }

    public function getVersiumGooglePassword() {
        return $_ENV['VERSIUM_GOOGLE_PASSWORD'];
    }

    public function getTestUserPassword() {
        return $_ENV['REACH_TEST_USER_PASSWORD'];
    }

    public function getDownloadDir() {
        return $_ENV['SELENIUM_DOWNLOAD_DIR'];
    }

    public function sendKeys($input) {
        $this->getModule('WebDriver')->webDriver->getKeyboard()->sendKeys($input);
    }

    public function grabFullUrl() {
        $url = $this->getModule('WebDriver')->webDriver->getCurrentURL();
        return $url;
    }

    /** Versium.com */

    public function getVersiumDotComEnv() {
        return $this->config['environment'];
    }

    public function getVersiumDotComStgUsername() {
        return $_ENV['VERSIUM_DOT_COM_STG_USERNAME'];
    }

    public function getVersiumDotComStgPassword() {
        return $_ENV['VERSIUM_DOT_COM_STG_PASSWORD'];
    }
}
