<?php

namespace Tests\Support;

/**
 * Inherited Methods
 * @method void wantToTest($text)
 * @method void wantTo($text)
 * @method void execute($callable)
 * @method void expectTo($prediction)
 * @method void expect($prediction)
 * @method void amGoingTo($argumentation)
 * @method void am($role)
 * @method void lookForwardTo($achieveValue)
 * @method void comment($description)
 * @method void pause()
 *
 * @SuppressWarnings(PHPMD)
*/
class ApiTester extends \Codeception\Actor
{
    use _generated\ApiTesterActions;


    # Define custom actions here

    // Environments
    const STAGING = 'staging';
    const PROD = 'prod';

    const VERSIUM_API_KEY_HEADER = 'X-Versium-Api-Key';

    // API return types
    const DEMOGRAPHIC_TYPE = [
        "DOB"                           => 'string',
        "Age Range"                     => 'string',
        "Gender"                        => 'string',
        "Education Level"               => 'string',
        "Ethnic Group"                  => 'string',
        "Religion"                      => 'string',
        "Occupation"                    => 'string',
        "Language"                      => 'string',
        "Marital Status"                => 'string',
        "Working Woman in Household"    => 'string',
        "Senior in Household"           => 'string',
        "Single Parent"                 => 'string',
        "Presence of Children"          => 'string',
        "Number of Children"            => 'string',
        "Young Adult in Household"      => 'string',
        "Small Office or Home Office"   => 'string',
        "Online Purchasing Indicator"   => 'string',
        "Online Education"              => 'string',
        "Individual Level Match"        => 'string'
    ];

    const FINANCIAL_TYPE = [
        "Home Own or Rent"                  => 'string',
        "Household Income"                  => 'string',
        "Estimated Net Worth"               => 'string',
        "Home Year Built"                   => 'string',
        "Home Purchase Date"                => 'string',
        "Dwelling Type"                     => 'string',
        "Home Value"                        => 'string',
        "Length of Residence"               => 'string',
        "Credit Card Holder Bank"           => 'string',
        "Upscale Card Holder"               => 'string',
        "Credit Rating"                     => 'string',
        "Mortgage Purchase Amount"          => 'string',
        "Mortgage Purchase Loan Type"       => 'string',
        "Mortgage Purchase Date"            => 'string',
        "2nd Most Recent Mortgage Amount"   => 'string',
        "2nd Most Recent Mortgage Loan Type"=> 'string',
        "2nd Most Recent Mortgage Date"     => 'string',
        "Loan to Value"                     => 'string',
        "Refinance Date"                    => 'string',
        "Refinance Amount"                  => 'string',
        "Refinance Loan Type"               => 'string',
        "Individual Level Match"            => 'string'
    ];

    const LIFESTYLE_TYPE = [
        "Magazines"                     => 'string',
        "Reading"                       => 'string',
        "Current Affairs and Politics"  => 'string',
        "Mail Order Buyer"              => 'string',
        "Dieting and Weight Loss"       => 'string',
        "Travel"                        => 'string',
        "Music"                         => 'string',
        "Consumer Electronics"          => 'string',
        "Arts"                          => 'string',
        "Antiques"                      => 'string',
        "Home Improvement"              => 'string',
        "Gardening"                     => 'string',
        "Cooking"                       => 'string',
        "Exercise"                      => 'string',
        "Sports"                        => 'string',
        "Outdoors"                      => 'string',
        "Womens Apparel"                => 'string',
        "Mens Apparel"                  => 'string',
        "Pets"                          => 'string',
        "Investing"                     => 'string',
        "Health and Beauty"             => 'string',
        "Decorating and Furnishing"     => 'string',
        "Individual Level Match"        => 'string'
    ];

    const POLITICAL_TYPE = [
        "Donor Environmental"               => 'string',
        "Donor Animal Welfare"              => 'string',
        "Donor Arts and Culture"            => 'string',
        "Donor Childrens Causes"            => 'string',
        "Donor Environmental or Wildlife"   => 'string',
        "Donor Health"                      => 'string',
        "Donor International Aid"           => 'string',
        "Donor Political"                   => 'string',
        "Donor Conservative Politics"       => 'string',
        "Donor Liberal Politics"            => 'string',
        "Donor Religious"                   => 'string',
        "Donor Veterans"                    => 'string',
        "Donor Unspecified"                 => 'string',
        "Donor Community"                   => 'string',
        "Individual Level Match"            => 'string',
    ];

    const B2C_CONTACT_APPEND_ADDRESS_RETURN_TYPE = [
        'First Name'      => 'string',
        'Last Name'       => 'string',
        'Postal Address'  => 'string',
        'City'            => 'string',
        'State'           => 'string',
        'Zip'             => 'string',
        'Country'         => 'string'
    ];

    const B2C_CONTACT_APPEND_PHONE_BEST_RETURN_TYPE = [
        'First Name'    => 'string',
        'Last Name'     => 'string',
        'Phone'         => 'string',
        'Line Type'     => 'string'
    ];

    const B2C_CONTACT_APPEND_PHONE_MOBILE_RETURN_TYPE = [
        'First Name'    => 'string',
        'Last Name'     => 'string',
        'Mobile Phone'  => 'string'
    ];

    const B2C_CONTACT_APPEND_PHONE_MULTIPLE_RETURN_TYPE = [
        'First Name'      => 'string',
        'Last Name'       => 'string',
        'Phone'           => 'string',
        'Line Type'       => 'string',
        'Alt Phone 1'     => 'string',
        'Alt Phone 2'     => 'string',
        'Alt Phone 3'     => 'string',
        'Alt Phone 4'     => 'string',
        'Alt Phone 5'     => 'string',
        'Alt Line Type 1' => 'string',
        'Alt Line Type 2' => 'string',
        'Alt Line Type 3' => 'string',
        'Alt Line Type 4' => 'string',
        'Alt Line Type 5' => 'string',
    ];

    const B2C_CONTACT_APPEND_EMAIL_OUTPUT_RETURN_TYPE = [
        'First Name'    => 'string',
        'Last Name'     => 'string',
        'Email Address' => 'string'
    ];

    const C2B_OUTPUT_TYPE = [
        "Title"             => "string",
        "First Name"        => "string",
        "Last Name"         => "string",
        "Email Address"     => "string",
        "Business"          => "string",
        "Postal Address"    => "string",
        "City"              => "string",
        "State"             => "string",
        "Zip"               => "string",
        "Domain"            => "string",
        "Number of Employees"=> "string",
        "Sales Volume"      => "string",
        "Year Founded"      => "string",
        "SIC"               => "string",
        "SIC Description"   => "string",
        "NAICS"             => "string",
        "NAICS Description" => "string",
        "Public or Private" => "string",
    ];

    const FIRMOGRAPHIC_OUTPUT_TYPE = [
        "Business"            => 'string',
        "Postal Address"      => 'string',
        "City"                => 'string',
        "State"               => 'string',
        "Zip"                 => 'string',
        "Country"             => 'string',
        "Phone"               => 'string',
        "Domain"              => 'string',
        "Website Home Page"   => 'string',
        "Number of Employees" => 'string',
        "Sales Volume"        => 'string',
        "Year Founded"        => 'string',
        "SIC"                 => 'string',
        "SIC Description"     => 'string',
        "NAICS"               => 'string',
        "NAICS Description"   => 'string',
        "Public or Private"   => 'string',
    ];

    const IP2DOMAIN_OUTPUT_TYPE = [
        "IP Usage Type"         => 'string',
        "Is ISP?"               => 'string',
        "Domain 1"              => 'string',
        "Company Name 1"        => 'string',
        "Company Address 1"     => 'string',
        "Company City 1"        => 'string',
        "Company State 1"       => 'string',
        "Company Zip 1"         => 'string',
        "Company Country 1"     => 'string',
        "Phone 1"               => 'string',
        "Website Home Page 1"   => 'string',
        "Employee Range 1"      => 'string',
        "Sales Revenue Range 1" => 'string',
        "Year Founded 1"        => 'string',
        "SIC 1"                 => 'string',
        "SIC Description 1"     => 'string',
        "NAICS 1"               => 'string',
        "NAICS Description 1"   => 'string',
        "Public/Private 1"      => 'string',
        "Domain 2"              => 'string',
        "Company Name 2"        => 'string',
        "Company Address 2"     => 'string',
        "Company City 2"        => 'string',
        "Company State 2"       => 'string',
        "Company Zip 2"         => 'string',
        "Company Country 2"     => 'string',
        "Phone 2"               => 'string',
        "Website Home Page 2"   => 'string',
        "Employee Range 2"      => 'string',
        "Sales Revenue Range 2" => 'string',
        "Year Founded 2"        => 'string',
        "SIC 2"                 => 'string',
        "SIC Description 2"     => 'string',
        "NAICS 2"               => 'string',
        "NAICS Description 2"   => 'string',
        "Public/Private 2"      => 'string',
        "Domain 3"              => 'string',
        "Company Name 3"        => 'string',
        "Company Address 3"     => 'string',
        "Company City 3"        => 'string',
        "Company State 3"       => 'string',
        "Company Zip 3"         => 'string',
        "Company Country 3"     => 'string',
        "Phone 3"               => 'string',
        "Website Home Page 3"   => 'string',
        "Employee Range 3"      => 'string',
        "Sales Revenue Range 3" => 'string',
        "Year Founded 3"        => 'string',
        "SIC 3"                 => 'string',
        "SIC Description 3"     => 'string',
        "NAICS 3"               => 'string',
        "NAICS Description 3"   => 'string',
        "Public/Private 3"      => 'string',
    ];

    public function setHeaders(ApiTester $I) {
        if ($I->getEnv() === $I::STAGING) {
            $I->haveHttpHeader($I::VERSIUM_API_KEY_HEADER, $I->getAPIStagingKey());
        }
        else if ($I->getEnv() === $I::PROD) {
            $I->haveHttpHeader($I::VERSIUM_API_KEY_HEADER, $I->getAPIProdKey());
        }
        $I->haveHttpHeader('accept', 'application/json');
        $I->haveHttpHeader('content-type', 'application/json');
    }

    public function seeResponseIsOK(ApiTester $I) {
        $I->seeResponseIsJson();
        $I->seeResponseCodeIs(200);
        $I->dontSeeResponseJsonMatchesJsonPath('versium.errors');
    }

    public function seeResultsIsNotEmpty(ApiTester $I) {
        $results = $I->grabDataFromResponseByJsonPath('versium.results[0]');
        $I->assertNotEmpty($results);
    }
}
