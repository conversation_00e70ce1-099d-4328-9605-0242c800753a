<?php

namespace Tests\Support;

/**
 * Inherited Methods
 * @method void wantToTest($text)
 * @method void wantTo($text)
 * @method void execute($callable)
 * @method void expectTo($prediction)
 * @method void expect($prediction)
 * @method void amGoingTo($argumentation)
 * @method void am($role)
 * @method void lookForwardTo($achieveValue)
 * @method void comment($description)
 * @method void pause()
 *
 * @SuppressWarnings(PHPMD)
 */
class KittiverseTester extends \Codeception\Actor
{
    use _generated\KittiverseTesterActions;

    private static $sqlConnection = null;
    private static $kvTruthData = null;

    /**
     * Define custom actions here
     */

    function assertInExpectedRange($truth, $actual, $percentage)
    {
        $I = $this;

        $I->wantToTest("$actual is within $percentage range of $truth");
        $diff = abs($actual - $truth);
        // codecept_debug($diff);

        // codecept_debug('Diff percentage is: ');
        $diffpercent = ($diff / $actual) * 100;
        // codecept_debug($diffpercent);
        $I->assertLessThanOrEqual($percentage, $diffpercent,
            "Change in records $actual from $truth exceed threshold $percentage percent");
    }


    private function getsqlConnection()
    {
        $I = $this;

        if (self::$sqlConnection == null) {

            // Create connection
            self::$sqlConnection = new \mysqli($I->getServer(), $I->getUserName(), $I->getPassword(), $I->getDBName());

            // Check connection
            if (self::$sqlConnection->connect_error) {
                die("Connection failed: " . self::$sqlConnection->connect_error);
            }

        }
        return self::$sqlConnection;
    }


    public function executeQuery($query)
    {
        $conn = $this->getsqlConnection();
        echo "\nExecuting query: \n $query";
        $result = $conn->query($query);
        echo "\nResults:\n";
        return $result;
    }

    public function executeQueryGetAssocArray($query)
    {

        $result = $this->executeQuery($query);
        $rows = array();
        while ($r = $result->fetch_assoc()) {
            $rows[] = $r;
        }
        return $rows;
    }

    public function executeQueryGetAssocArrayValueCount($query)
    {
        $result = $this->executeQuery($query);
        $rows = [];
        while ($r = $result->fetch_assoc()) {
            $keys = array_keys($r);
            $rows[$r[$keys[0]]] = $r[$keys[1]];
        }
        return $rows;
    }

    function executeQueryCount($query, $fieldName)
    {
        $result = $this->executeQuery($query);

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            # print result to console
            print_r($row);
            return $row[$fieldName];
        }
        return -999;
    }

    function getDistinctQueryForKey($key)
    {
        return sprintf('select count(distinct(`%s`)) as %%s from `KittiversePINDIV`', $key);
    }

    function getNotEmptyQueryForKey($key)
    {
        return sprintf('select count(`%s`) as %%s from `KittiversePINDIV` where `%s` <> \'\'', $key, $key);
    }

    function getKVTruthData()
    {
        $path = codecept_data_dir() . KV_DATA_DIR . KV_TRUTH_FILE;

        if (self::$kvTruthData == null) {
            self::$kvTruthData = json_decode(file_get_contents($path), true);
        }
        return self::$kvTruthData;
    }

    function getKVTruthDataField($fieldName)
    {
        $kvData = $this->getKVTruthData();
        return $kvData[$fieldName];
    }

    function getKVTruthTotalRecordCount()
    {
        return $this->getKVTruthDataField(TOTAL_RECORD_COUNT);
    }

    function getKVbadCharacterInEmailRecordCount()
    {
        return $this->getKVTruthDataField(BAD_CHAR_IN_EMAIL_RECORD_COUNT);
    }

    function getKVUniqueDomainKeyCount()
    {
        return $this->getKVTruthDataField(UNIQUE_DOMAIN_KEY_COUNT);
    }

    function getKVUniqueCorpDomainCount()
    {
        return $this->getKVTruthDataField(UNIQUE_CORP_DOMAIN_COUNT);
    }

    function getKVUniqueCorpNameCount()
    {
        return $this->getKVTruthDataField(UNIQUE_CORP_NAME_COUNT);
    }

    function getKVUniqueEmailCount()
    {
        return $this->getKVTruthDataField(UNIQUE_EMAIL_COUNT);
    }

    function getKVUniqueSICCount()
    {
        return $this->getKVTruthDataField(UNIQUE_SIC_COUNT);
    }

    function getKVLastNameFirstNameMatchCount()
    {
        return $this->getKVTruthDataField(LAST_NAME_FIRST_NAME_MATCH_COUNT);
    }

    function getKVEmailWithBadCharCount()
    {
        return $this->getKVTruthDataField(BAD_CHAR_IN_EMAIL_RECORD_COUNT);
    }

    function getKVDomainsWithoutNumEmployeesCount()
    {
        return $this->getKVTruthDataField(DOMAINS_WITHOUT_NUMEMPLOYEES_COUNT);
    }

    function getKVDomainsWithoutSalesCount()
    {
        return $this->getKVTruthDataField(DOMAINS_WITHOUT_SALES_COUNT);
    }

    function getKVDomainsWitTickerCount()
    {
        return $this->getKVTruthDataField(DOMAINS_WITH_TICKER_COUNT);
    }

    function getKVRecordsWitTickerCount()
    {
        return $this->getKVTruthDataField(RECORDS_WITH_TICKER_COUNT);
    }

    public function disConnect()
    {
        $this->getsqlConnection()->close();
    }

    public function getKVDomainsOver500WithCount()
    {
        return $this->getFileWithCountContent(DOMAINS_OVER_500_FILE);
    }

    public function getKVColumnsCount()
    {
        return $this->getFileWithCountContent(COLUMNS_COUNT_FILE);
    }

    public function getKVBuildColumnsCount()
    {
        return $this->getFileWithCountContent(KV_COLUMNS_COUNT_FILE);
    }

    public function getFileWithCountContent($filename) {
        $path = codecept_data_dir() . KV_DATA_DIR . $filename;
        $contentStr = file_get_contents($path);
        $lines = explode(PHP_EOL, $contentStr);

        $result = array();
        foreach ($lines as $line) {
            if ($line) {
                $lcsv = str_getcsv($line);
                $result[$lcsv[0]] = $lcsv[1];
            }
        }

        return $result;
    }

    public function getDataFromFileToList($fileName, $parseFunc) {
        $path = codecept_data_dir() . KV_DATA_DIR . $fileName;
        $linesStr = file_get_contents($path);
        $lines = explode(PHP_EOL, $linesStr);

        $result = array();
        foreach ($lines as $line) {
            $lcsv = str_getcsv($line);
            $result[] = $parseFunc($lcsv);
        }

        return $result;
    }


    public function getLiuidList() {
        return $this->getDataFromFileToList(LIUID_FILE, function ($lcsv) {
            return $lcsv[0];
        });
    }


    public function getLIProfileURLList() {
        return $this->getDataFromFileToList(LIUID_FILE, function ($lcsv) {
            return str_replace("http://www.linkedin.com/in/", "", $lcsv[5]);
        });
    }

}
