<?php

namespace Tests\Support;

ini_set('memory_limit', '40000M');

/**
 * Inherited Methods
 * @method void wantToTest($text)
 * @method void wantTo($text)
 * @method void execute($callable)
 * @method void expectTo($prediction)
 * @method void expect($prediction)
 * @method void amGoingTo($argumentation)
 * @method void am($role)
 * @method void lookForwardTo($achieveValue)
 * @method void comment($description)
 * @method void pause()
 *
 * @SuppressWarnings(PHPMD)
 */

class ConverseTester extends \Codeception\Actor
{
    use _generated\ConverseTesterActions;

    private static $sqlConnection = null;

    public static $duplicatePhoneNumbersToTrack = [
        8054578087,
        6062050082,
        3176258495,
        7652420607,
        7244457844 // currently 0 dupes
    ];

    public static $duplicateEmailsToTrack = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ];

    public static $politicalPartiesToTrack = [
        'DEMOCRAT',
        'GREEN',
        'INDEPENDENT',
        'LIBERTARIAN',
        'NO AFFILIATION',
        'REFORM',
        'REPUBLICAN'
    ];

    public static $educationCodesToTrack = [
        'A', 'B', 'C', 'D'
    ];

    public static $netWorthCodesToTrack = [
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'
    ];

    public static $numberOfChildrenToTrack = [
        0, 1, 2, 3, 4, 7
    ];

    public static $ownerRenterKeysToTrack = [
        'H', 'R', '9'
    ];


    private function getsqlConnection()
    {
        $I = $this;

        if (self::$sqlConnection == null) {
            // Create connection
            self::$sqlConnection = new \mysqli(
                $I->getServer(),
                $I->getUserName(),
                $I->getPassword(),
                $I->getDBName()
            );
            // Check connection
            if (self::$sqlConnection->connect_error) {
                die("Connection failed: " . self::$sqlConnection->connect_error);
            }
        }
        return self::$sqlConnection;
    }

    public function executeQuery($query)
    {
        $conn = $this->getsqlConnection();
        echo "\nExecuting the query:\n $query";
        $result = $conn->query($query);
        echo "\nDone.\n";
        return $result;

    }

    public function executeQueryCount($queryFormatString, $fieldName)
    {
        $query = sprintf($queryFormatString, $fieldName);
        $result = $this->executeQuery($query);
        // print_r($result);

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            print_r($row);
            return $row[$fieldName];
        }
        return -999;
    }

    public function buildResultsMap($rows)
    {
        $resultsMap = array();
        foreach($rows as $key => $val) {
            $label = $rows[$key][0];
            $count = $rows[$key][1];
            $resultsMap[$label] = $count;
        }
        return $resultsMap;
    }

    public function echoResults($resultsMap, $arrayOfKeys)
    {
        foreach($arrayOfKeys as $label) {
            $count = $resultsMap[$label] ?? 'N/A';
            echo "\n $label : $count \n";
        }
    }
}
