<?php

namespace Tests\Support\Classes;

// Each demo field is an array of arrays.
// The shape of inner array is "input_param" followed by the specific filter name, Ex: ['d_refi_loan_type', 'FHA']

class ListGenDemoFilters {
	public ?array $demographic;
	public ?array $houseFinAuto;
	public ?array $lifestyleInterest;
	public ?array $politicalDonor;


	public function __construct(
		?array $demographic = null,
		?array $houseFinAuto = null,
		?array $lifestyleInterest = null,
		?array $politicalDonor = null
	) {
		$this->demographic = $demographic;
		$this->houseFinAuto = $houseFinAuto;
		$this->lifestyleInterest = $lifestyleInterest;
		$this->politicalDonor = $politicalDonor;
	}
}
