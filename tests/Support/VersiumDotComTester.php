<?php

declare(strict_types=1);

namespace Tests\Support;

use Tests\Support\Page\Versium\HomePage;

/**
 * Inherited Methods
 * @method void wantTo($text)
 * @method void wantToTest($text)
 * @method void execute($callable)
 * @method void expectTo($prediction)
 * @method void expect($prediction)
 * @method void amGoingTo($argumentation)
 * @method void am($role)
 * @method void lookForwardTo($achieveValue)
 * @method void comment($description)
 * @method void pause($vars = [])
 *
 * @SuppressWarnings(PHPMD)
*/
class VersiumDotComTester extends \Codeception\Actor
{
    use _generated\VersiumDotComTesterActions;

    /**
     * Define custom actions here
     */

    public function acceptCookies(VersiumDotComTester $I) {
        $I->waitForElementAndClick(HomePage::$acceptCookiesBtn);
        $I->wait(1);
    }

    public function waitForElementAndClick($element, $timeout = 60) {
        $I = $this;

        $I->waitForElement($element, $timeout);
        $I->click($element);
    }
}
