<?php

namespace Tests\Support\Page;

class PaymentDetails {

    public static $paymentDetailsHeader = "//h1[text()='Payment Details']";
    public static $addNewCardBtn = "//button[text()='Add New Card']";

    // Add CC Form
    public static $emailInput = "//input[@name='email']";
    public static $nameInput = "//input[@name='name']";
    public static $addrInput = "//input[@name='address1']";
    public static $addr2Input = "//input[@name='address2']";
    public static $cityInput = "//input[@name='city']";
    public static $stateDropdown = "//select[@name='state']";
    public static $zipInput = "//input[@name='zip']";
    public static $countryDropdown = "//select[@name='country']";
    public static $ccNnumberInput = "//input[@autocomplete='cc-number']";
    public static $continueBtn = "//button[text()='Add Card and Continue']";

    // CC Buttons
    public static $deleteCardBtnFormatString = "//span[text()='%s']/ancestor::div[contains(@class, 'vs-card-list-item')]//button[text()='Delete Card']";
    public static $editEmailBtnFormatString = "//span[text()='%s']/ancestor::div[contains(@class, 'vs-card-list-item')]//button[text()='Edit']";
    public static $billingEmailInputFormatString = "//span[text()='%s']/ancestor::div[contains(@class, 'vs-card-list-item')]//input[@name='emailText']";
    public static $billingEmailSaveBtnFormatString = "//span[text()='%s']/ancestor::div[contains(@class, 'vs-card-list-item')]//button[text()='Save']";
    public static $makePrimaryBtnFormatString = "//span[text()='%s']/following-sibling::button[text()='Make Primary']";
    public static $primaryLabelFormatString = "//span[text()='%s']/following-sibling::span[text()='Primary']";

    // Confirmation Modal
    public static $deleteConfirmationBtn = "//button[text()='Yes, delete this card']";

    // Mock Stripe Authentication Popup

    // Toasts
    public static $cardSavedToast = "//div[@class='Toastify']//div[text()='Card info saved']";
    public static $cardDeletedToast = "//div[@class='Toastify']//div[text()='Card deleted']";
    public static $billingEmailUpdatedToast = "//div[@class='Toastify']//div[text()='Billing email updated']";
    public static $primaryCardToast = "//div[@class='Toastify']//div[text()='Card made primary payment method']";
    public static $transactionsEnabledToast = "//div[@class='Toastify']//div[text()='Account Upgraded. You can now purchase individual lists and audiences.']";


}