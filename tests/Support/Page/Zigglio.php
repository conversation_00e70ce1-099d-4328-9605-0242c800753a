<?php

namespace Tests\Support\Page;

class Zigglio {

    // Add/Remove Zigglio Integration
    public static $connectBtn = "//a[text()='Activate']";
    public static $disconnectBtn = "//button[text()='Deactivate']";
    public static $disconnectBtnFinal = "//a[text()='Deactivate']";

    // Add/Remove Ad Accounts
        // Facebook
    public static $connectAdAccountBtn = "//a[@id='SaveFBAdAccount']";
    public static $removeAdAccountBtn = "//button[text()='Remove Ad Account']";
    public static $removeAdAccountBtnFinal = "//a[text()='Remove Ad Account']";
    public static $adAccountSelect = "//select[@id='ddlManagedAdAccounts']";
    public static $adAccountOption = 'Datafinder'; // 'Versium' is alternative
        // Google
    public static $googleCustomerSelect = "//select[@id='ddlManagedCustomers']";
    public static $googleCustomerOption = 'Versium REACH';
    public static $googleClientAcctSelect = "//select[@id='ddlManagedAccounts']";
    public static $googleSaveAdAcctBtn = "//a[@id='SaveAdwordsAccount']";
    public static $googleRemoveAdAcctBtn = "//button[@onclick='toggleRemoveAdwordsAdAccount()']";
    public static $googleRemoveAdAcctBtnFinal = "//a[text()='Remove Ad Account']";

    // 3rd Party Login
        // Facebook
    public static $fbUsernameInput = "//input[@id='email']";
    public static $fbPasswordInput = "//input[@id='pass']";
    public static $fbLoginBtn = "//button[@id='loginbutton']";
    public static $fbContinueBtn = "//div[@aria-label='Continue as Tristan Bull']";
    public static $fbNextBtn = "//div[@aria-label='Next']";
    public static $fbDoneBtn = "//div[@aria-label='Done']";
    public static $fbOkBtn = "//div[@aria-label='OK']";
        // Google
    public static $GoogleUsernameInput = "//input[@type='email']";
    public static $GooglePasswordInput = "//input[@type='password']";
    public static $GoogleNxtBtn = "//span[text()='Next']//parent::button";
    public static $GoogleAllowAccessBtn = "//span[text()='Allow']//parent::button";

    // Toasts
        // Facebook
    public static $removeAdAccountSuccessToast = "//div[text()='You have successfully removed your Ad Account']";
    public static $connectSuccessToast = "//div[text()='Your Facebook account was integrated successfully.']";
        // Google
    public static $googleConnectSuccessToast = "//div[text()='Your AdWords account was integrated successfully.']";
    public static $googleRemoveAdAcctSuccessToast = "//div[text()='You have successfully removed your Adwords Account']";


    // Text
    public static $zigglioBrandText = 'Powered by Zigglio';
        // Facebook
    public static $zigglioFBAdsText = 'Facebook Ads';
    public static $connectAdAccountText = 'Please connect your Ad Account';
    public static $deactivationWarningText = 'Warning: Deactivation will remove all your audiences';
    public static $removeAdAccountWarningText = 'Warning: Removing Ad Account will delete all audiences created within Zigglio. Your audiences will still be available on Facebook';
        // Google
    public static $zigglioGoogleAdsText = 'Google Ads';
    public static $googleSelectCustomerText = 'Please select your Customer';
    public static $googleSelectAcctText = 'Please select your Client Account';
    public static $removeGoogleAdAcctWarningText = 'Warning: Removing Ad Account will delete all audiences created within Zigglio. Your audiences will still be available on Google Ads';
}