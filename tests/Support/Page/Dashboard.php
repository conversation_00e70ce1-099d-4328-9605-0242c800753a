<?php

namespace Tests\Support\Page;


class Dashboard
{
    # MarketingStar Promo Modal
    public static $MSGetStartedBtn = "//a[@href='https://versium.com/versium-reach-x-marketing-star' and text()='Get Started']";
    public static $MSCloseModalBtn = "//div[@class='modal-content']/button";

    # MarketingStar Card
    public static $marketingStarCard = "//h3[text()='Subscriber Exclusive']/parent::a";
    public static $marketingStarLogo = "//img[@src='/assets/marketing-star-logo-color-Cs_4CHmQ.svg']";

    # Header
    public static $versiumReachImage = "//img[@src='/static/media/versium-reach-light.1cba2388.svg']";
    public static $homeLink = "//a[@href='/']";
    # Account Settings
    public static $accountSettingsButton = "//nav//button[@class='dropdown-toggle bg-transparent border-0 d-flex align-items-center shadow-none text-white']"; //TODO: add ID to ui
    public static $accountSettingsLink = "//a[@href='/account']";
    public static $adminUsersLink = "//a[@href='/admin-users']";
    public static $logoutText = 'Log Out';
    # Account Info
    public static $accountInfoTitle = 'Account Info';
    public static $accountInfoStart = 'Contract Start Date';
    public static $accountInfoEnd = 'Next Billing Date';
    public static $accountInfoMoreCredits = 'More Match Credits on';
    public static $accountInfoCreditsAvailable = 'Match Credits Available';

    # Project Tools
    # Tabs
    public static $consumerToolsTab = "//a[text()='Consumer Data']";
    public static $businessToolsTab = "//a[text()='Business Data']";
    public static $dataHygieneTab = "//a[text()='Data Hygiene']";
    # Business Data Tools
    public static $listInsightsCard = "//a[@href='/projects/create/list-insights']";
    public static $personaListCard = "//a[@href='/projects/create/persona-list']";
    public static $abmTargetedListCard = "//a[@href='/projects/create/abm-list']";
    public static $lookAlikeListCard = "//a[@href='/projects/create/look-alike-list']";
    public static $onlineAudienceCard = "//a[@href='/projects/create/online-audience']";
    public static $firmographicCard = "//a[@href='/projects/create/firmographic-append']";
    public static $ip2DomainCard = "//a[@href='/projects/create/ip-to-domain']";
    public static $ip2DomainLiteCard = "//a[@href='/projects/create/ip-to-domain-lite']";
    public static $c2bAppendCard = "//a[@href='/projects/create/c2b-append']";
    # Consumer Data Tools
    public static $contactAppendCard = "//a[contains(@href, '/projects/create/contact-append')]";
    public static $demographicAppendCard = "//a[@href='/projects/create/demographic-append']";
    public static $b2cOnlineAudienceCard = "//a[@href='/projects/create/b2c-online-audience']";
    public static $b2cListInsightsCard = "//a[@href='/projects/create/b2c-list-insights']";
    public static $listGenCard = "//a[@href='/projects/create/b2c-people-list']";
    # Data Hygiene Tools
    public static $dataPrepCard = "//a[@href='/projects/create/data-workbench']";
    public static $dedupeCard = "//a[@href='/projects/create/dedupe']";
    public static $emailValidationCard = "//a[@href='/projects/create/email-validation']";

    # Versium Announcements
    public static $announcementsHeader = "//h3[text()='Versium Announcements']";
    public static $announcementsSubHeader1 = "//h4[text()='Multi-Factor Authentication (MFA)']";
    public static $announcementsSubHeader2 = "//h4[text()='Try our updated Consumer Audience Builder!']";
    public static $announcementsSubHeader3 = "//h4[text()='Now Available Free: Dedupe Your Audience Data!']";

    # Quick Resources
    public static $accordianHeaderFormatString = "//div[@class='vs-accordion-header' and text()='%s']";
    public static $quickResourcesHubspotLink = "//a[text()='Go to Hubspot']";
    public static $quickResourcesZapierLink = "//a[text()='Explore Zapier']";
    public static $quickResourcesReachDocsLink = "//a[text()='REACH Documentation']";
    public static $quickResourcesSuppressionLink = "//a[text()='Exclude Contacts from Campaigns']";
    public static $quickResourcesSurveyLink = "//a[text()='Take our quick survey']";

    # App Version
    public static $appVersionElement = "//span[@id='vs-app-version']";
    public static $appVersionToast = "//div[@class='Toastify__toast Toastify__toast-theme--colored Toastify__toast--info vs-app__version-toast border border-secondary text-black shadow-sm px-3 pb-4']";
    public static $appVersion = 'V7.0.0';
    public static $appVersionText = 'Bionic Man Edition';

    # Trial End
    public static $trialEndBar = "//div[@class='vs-trial-upgrade-bar font-weight-bolder text-white text-center d-flex flex-column bg-success']";
    public static $viewOptionsBtn = "//button[@class='vs-trial-upgrade-bar__upgrade-button bg-transparent border-0 p-0 text-white font-weight-bolder shadow-none']";
    public static $trialModalHeaderText = 'Looks like you want to upgrade';
    public static $enterCardDetailsBtn = "//button[text()='Choose Pay as You Go']";
    public static $contactSalesBtn = "//button[text()='Contact Sales']";
    public static $contactSalesSuccessText = 'Thank you for choosing Versium REACH';
    public static $contactSalesSuccessCloseBtn = "button[text()='You can close this window']";

    # Usage Snapshot Widget
    public static $usageSnaphotTitle = 'Usage Snapshot';
    public static $usageSnapshotMatchesUsed = "//div[contains(text(), 'Matches used')]";
    public static $usageSnapshotSVG = "//*[local-name()='svg' and @class='main-svg']";
    public static $usageSnapshotInfoSVG = "//*[local-name()='svg' and @data-icon='regular-calendar-days-circle-info']";


    private static $consumerOpsAndBreadcrumbs = null;
    private static $businessOpsAndBreadcrumbs = null;
    private static $dataHygieneAndBreadcrumbs = null;
    private static $accountInfoStrings = null;
    private static $announcementElems = null;
    private static $quickResourcesLinks = null;

    public static function consumerOpsAndBreadcrumbs() {
        if (self::$consumerOpsAndBreadcrumbs == null) {
            self::$consumerOpsAndBreadcrumbs = [
                # b2c
                self::$b2cListInsightsCard => 'Create List Insights',
                self::$contactAppendCard => 'Create a Contact Append',
                self::$demographicAppendCard => 'Create a Demographic Append',
                self::$b2cOnlineAudienceCard => 'Create a B2C Online Audience',
                self::$listGenCard => 'Choose Filters'
            ];
        }
        return self::$consumerOpsAndBreadcrumbs;
    }

    public static function bussinessOpsAndBreadcrumbs() {
        if (self::$businessOpsAndBreadcrumbs == null) {
            self::$businessOpsAndBreadcrumbs = [
                # b2b
                self::$listInsightsCard => 'Create List Insights',
                self::$personaListCard => 'Create a Persona List',
                self::$abmTargetedListCard => 'Create an Account-based Audience',
                self::$lookAlikeListCard => 'Create a Look-alike List',
                self::$onlineAudienceCard => 'Create a B2B Online Audience',
                self::$firmographicCard => 'Create a Firmographic Append',
                self::$ip2DomainCard => 'Create IP to Domain',
                // self::$ip2DomainLiteCard => 'Create IP to Domain Lite',
                self::$c2bAppendCard => 'Create a C2B Append',
            ];
        }
        return self::$businessOpsAndBreadcrumbs;
    }

    public static function dataHygieneAndBreadcrumbs() {
        if (self::$dataHygieneAndBreadcrumbs == null) {
            self::$dataHygieneAndBreadcrumbs = [
                self::$dataPrepCard => 'Data Prep',
                // self::$dedupeCard => 'Dedupe',
            ];
        }
        return self::$dataHygieneAndBreadcrumbs;
    }

    public static function getAccountInfoStrings()
    {
        if (self::$accountInfoStrings == null) {
            self::$accountInfoStrings = [
                self::$accountInfoTitle,
                self::$accountInfoStart,
                self::$accountInfoEnd,
                self::$accountInfoMoreCredits,
                self::$accountInfoCreditsAvailable,
            ];
        }
        return self::$accountInfoStrings;
    }

    public static function getAnnouncementElems()
    {
        if (self::$announcementElems == null) {
            self::$announcementElems = [
                self::$announcementsHeader,
                self::$announcementsSubHeader1,
                self::$announcementsSubHeader2,
                self::$announcementsSubHeader3,
            ];
        }
        return self::$announcementElems;
    }

    public static function getQuickResourcesElems()
    {
        if (self::$quickResourcesLinks == null) {
            self::$quickResourcesLinks = [
                'Zapier Automations' => self::$quickResourcesZapierLink,
                'REACH Help'         => self::$quickResourcesReachDocsLink,
                'Suppression Lists'  => self::$quickResourcesSuppressionLink,
            ];
        }
        return self::$quickResourcesLinks;
    }
}
