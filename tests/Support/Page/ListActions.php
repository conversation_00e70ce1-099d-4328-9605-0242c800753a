<?php

namespace Tests\Support\Page;

class ListActions {
    /**
     * B2C List Actions
     */
    public static $contactAppendBtn = "//span[contains(.,'Contact Append')]/ancestor::button";
    public static $demographicAppendBtn = "//span[text()='Demographic Append']/ancestor::button";
    public static $onlineAudienceAppendBtn = "//span[text()='Online Audience Append']/ancestor::button";
    public static $exportBtn = "//span[text()='Export']/ancestor::button";
    public static $previewBtn = "//span[text()='Preview']/ancestor::button";
    public static $renameBtn = "//span[text()='Rename']/ancestor::button";
    public static $deleteBtn = "//span[text()='Delete']/ancestor::button";

    private static $elements = null;

    public static function getElements() {
        if (self::$elements == null) {
            self::$elements = [
                self::$contactAppendBtn,
                self::$demographicAppendBtn,
                self::$onlineAudienceAppendBtn,
                self::$exportBtn,
                self::$previewBtn,
                self::$renameBtn,
                self::$deleteBtn
            ];
        }
        return self::$elements;
    }
}

?>