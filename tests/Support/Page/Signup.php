<?php

namespace Tests\Support\Page;

class Signup {
    public static $emailField = "//input[@name='email']";
    public static $passwordField = "//input[@name='password']";
    public static $firstNameField = "//input[@name='firstName']";
    public static $lastNameField = "//input[@name='lastName']";
    public static $phoneNumberField = "//input[@name='phone']";
    public static $jobTitleField = "//input[@name='jobTitle']";
    public static $countrySelect = "//select[@name='country']";
    public static $marketingTypeSelect = "//select[@name='marketingType']";
    public static $companyField = "//input[@name='company']";
    public static $companyTypeSelect = "//select[@name='companyType']";
    public static $monthlyAdvertisingSelect = "//select[@name='monthlySpend']";
    public static $demoSelect = "//select[@name='reachWalkthrough']";

    public static $createAccountBtn = "//button[@type='submit']";
    public static $forgotPasswordBtn = "//a[@href='/password-forgot']";
    public static $resetPasswordBtn = "//span[text()='Reset']/parent::button";

    public static $resetPasswordEmailSentText = 'Reset Password Email Sent';
    public static $resetPasswordEmailSentBlurb = 'If this email is associated with a Versium account, you will receive an email containing instructions on how to create a new password.';
    public static $resetPasswordSuccessText = 'Successfully reset <NAME_EMAIL>';

    // Password-Forgot
    public static $forgotPasswordInput = "//input[@name='email']";
    public static $sendResetInstructionsBtn = "//button/span[text()='Send Reset Instructions']";
    // Gmail
    public static $gmailEmailInput = "//input[@id='identifierId']";
    public static $gmailPasswordInput = "//input[@name='password']";
    public static $gmailNextBtn = "//span[text()='Next']/parent::button";

    // TODO: reformat this?
    public static $gmailEmailLink = "//span[text()='Reset your Versium REACH password']//parent::div//parent::td";

}

?>