<?php

namespace Tests\Support\Page;

class AdminUsers {


    public static $createAccountHeader = "//h1[text()='Create Account']";
    public static $createAccountLink = "//a[@href='/admin/create-account']";

    public static $searchBarInput = "//input[@class='form-control h-100 border-gray-300 rounded-right px-2']";
    public static $actionsBtn = "//button[@class='bg-transparent border-0 rounded-0 text-reset btn btn-secondary']";
    public static $managePlansBtn = "//button[text()='Manage plans']";

    # Add/Update Subscription Modal
    public static $subscriptionHeader = "//h3[text()='Add/Update a subscription']";
    public static $planSelect = "//select[@name='selectedPlan']";
    public static $monthlyMatchAllownceInput = "//input[@name='matchAllowance']";
    public static $toolsMultiSelect = "//select[@name='tools']";
    public static $contactAppendPlusLabel = "//label[@class='vs-checkbox position-relative d-flex align-items-center ']";
    public static $matchAllowFrequencySelect = "//select[@name='matchAllowanceFrequency']";
    public static $billingFrequecySelect = "//select[@name='selectedBillingFrequency']";

    public static $billingStartDateInput = "//label[text()='Billing start date']/parent::div//input[@id='startDateTime']";
    public static $cycleStartDateInput = "//label[text()='Cycle start date']/parent::div//input[@id='startDateTime']";
    public static $contractStartDateInput = "//label[text()='Contract start date']/parent::div//input[@id='startDateTime']";
    public static $contractEndDateInput = "//label[text()='Contract end date']/parent::div//input[@id='startDateTime']";

    public static $salesForceIdInput = "//input[@name='salesforceId']";
    public static $sfownerIdInput = "//input[@name='sfOwnerId']";
    public static $sfownerNameInput = "//input[@name='sfOwnerName']";

    public static $submitBtn = "//button[text()='Submit']";

    # Create New Account Form
    public static $firstNameInput = "//input[@name='first']";
    public static $lastNameInput =  "//input[@name='last']";
    public static $companyInput =  "//input[@name='company']";
    public static $emailInput =  "//input[@name='email']";
    public static $phoneInput =  "//input[@name='phone']";
    public static $passwordInput =  "//input[@name='password']";
    public static $productTypeSelect = "//select[@name='productType']";
    public static $makeTestUserLabel = "//label[text()='Make Test User']";

    public static $createAccountBtn = "//button[@class='d-block w-100 btn btn-primary rounded-pill my-4']";
    public static $successToast = "//div[@class='Toastify__toast Toastify__toast-theme--colored Toastify__toast--success vs-toast bg-black text-light rounded p-3 shadow-lg']";
}