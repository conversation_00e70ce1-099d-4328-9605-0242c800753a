<?php

namespace Tests\Support\Page;

class Persona
{
    public static $filterHeader = "//h5[text()='Create a Persona List']";
    // Campaign Types
    public static $onlineAudienceLabel = "//label[@for='listgen_output_option_onlineAudience']";
    public static $directEmailLabel = "//label[@for='listgen_output_option_directEmail']";
    public static $omnichannelLabel = "//label[@for='listgen_output_option_omnichannelMarketing']";
    // Industries
    public static $industriesTitle = 'Add Industries Filters';
    public static $industriesBtn = "//button[@id='d_sic']";
    public static $industrySearch = "//input[@class='form-control rounded']";
    public static $industryFormatString = "//span[contains(text(),'%s')]";
    // Employee Count
    public static $employeeCountLeftSlider = "//span[contains(text(),'Employee Count')]/following-sibling::div//div[@class='rc-slider-handle rc-slider-handle-1']";
    public static $employeeCountRightSlider = "//span[contains(text(),'Employee Count')]/following-sibling::div//div[@class='rc-slider-handle rc-slider-handle-2']";
    // Job Filters
    public static $titleSeniorityTitle = "//h3[text()='Title Seniority']";
    public static $titleSeniorityButton = "//button[@id='d_titlerank']";
    public static $titleSeniorityFormatString = "//span[text()='%s']";
    // Location Filters
    public static $usStatesTitle = "//div[text()='Add U.S. State Filters']";
    public static $usStatesBtn = "//button[@id='d_state']";
    public static $usStatesFormatString = "//span[text()='%s']";
    // General Buttons
    public static $closeButton = "//button[contains(text(),'Close')]";
    public static $nextStepButton = "//span[@id='vs-filters-and-preview__next-step']/parent::button[not(@disabled)]";


    // Persona Save List/Project Page
    public static $listNameInput = "//input[@name='newListName']";

}