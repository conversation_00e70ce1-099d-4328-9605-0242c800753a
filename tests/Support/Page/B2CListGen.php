<?php

namespace Tests\Support\Page;

class B2CListGen {

	/*************** INTRO STEP **********************/
	public static $footerBlurbElem = "//span[text()='What is Consumer List Generation?']";
	public static $dontShowAgainLabel = "//input[@name='hide_personaV2_intro']//parent::label";

	/*************** SELECT FILTERS **********************/
	public static $filtersPageTitle = "//h2[text()='Select Filters']";
	public static $applyBtn = "//button[text()='Apply']";
	public static $nextStepBtn = "//span[text()='Next Step']/parent::button";

	// Filter Categories
	public static $contactInfoBtn = "//div[contains(@class, 'vs-listgenV2-tab') and contains(text(), 1)]";
	public static $customizeLimitsBtn = "//div[contains(@class, 'vs-listgenV2-tab') and contains(text(), 2)]";
	public static $geographyBtn = "//div[contains(@class, 'vs-listgenV2-tab') and contains(text(), 3)]";
	public static $demographicBtn = "//div[contains(@class, 'vs-listgenV2-tab') and contains(text(), 4)]";
	public static $finAutoBtn = "//div[contains(@class, 'vs-listgenV2-tab') and contains(text(), 5)]";
	public static $lifestyleBtn = "//div[contains(@class, 'vs-listgenV2-tab') and contains(text(), 6)]";
	public static $politicalBtn = "//div[contains(@class, 'vs-listgenV2-tab') and contains(text(), 7)]";

	// 0. Intro
	public static $step1Header = "//div[@id='vs-wizard__step--1']//h2";
	public static $skipIntroLabel = "//input[@name='hide_personaV2_intro']/parent::label";


	// 1. Contact Info
	public static $contactInfoTitle = "Select Contact Info";
	public static $recommendationsLink = "";
	public static $includeAddrToggle = "//input[@id='personaV2-contact-inc-address']";
	public static $includePhoneToggle = "//input[@id='personaV2-contact-inc-phone']";
	public static $includeEmailToggle = "//input[@id='personaV2-contact-inc-email']";
	public static $includeOAToggle = "//input[@id='personaV2-contact-inc-oa']";

	// 2. Customize Limits
	public static $hhldRadio = "//input[@id='personaV2-hhld-multi']";
	public static $indivRadio = "//input[@id='personaV2-hhld-single']";
	public static $maxRecToggleLabel = "//label[@for='personaV2-limit-records-toggle']";
	public static $maxRecInputField = "//input[@type='number']";
	public static $skipEmailValidationLabel = "//label[@for='personaV2-skip-email-validation-toggle']";

	// 3. Geography Filters
	public static $cityRadio = "//input[@id='personaV2-filter-d_city_state']";
	public static $countyRadio = "//input[@id='personaV2-filter-d_county']";
	public static $stateRadio = "//input[@id='personaV2-filter-d_state']";
	public static $zipRadio = "//input[@id='personaV2-filter-d_zip']";
	public static $locationRadiusRadio = "//input[@id='personaV2-filter-cfg_zipradius']";

	public static $cityInputField = "//input[@placeholder='Search Cities']";
	public static $countyInputField = "//input[@placeholder='Search Counties']";
	public static $stateInputField = "//input[@placeholder='Search States']";
	public static $zipInputField = "//textarea[@class='w-100 h-100 overflow-auto']"; //TODO: give this textarea an id

	public static $geoFilterFormatString = "//label[contains(., '%s')]";

	// 4. Demographic
	public static $demoFilterRadioFormatString = "//input[contains(@id, '%s') and @type='radio']";
	public static $demoFilterLabelFormatString = "//label[contains(., '%s')]";
	public static $leftSliderHandleFormatString = "//div[@id='%s'] //div[@class='rc-slider-handle rc-slider-handle-1']";
	public static $rightSliderHandleFormatString = "//div[@id='%s'] //div[@class='rc-slider-handle rc-slider-handle-2']";

	// 5. Household, Finance, Auto
	public static $finAutoFilterRadioFormatString = "//input[contains(@id, '%s') and @type='radio']";
	public static $finAutoFilterLabelFormatString = "//input[@type='checkbox']/following-sibling::label[contains(., '%s')]";

	// 6. Lifestyle & Interests
	public static $lifestyleFilterLabelFormatString = "//label[text()=\"%s\"]";

	// 7. Political & Donor
	public static $politicalFilterRadioLabelFormatString = "//input[@type='radio']/following-sibling::label[text()='%s']";
	public static $politicalFilterLabelFormatString = "//label[text()=\"%s\"]";


	/*************** ATTRIBUTES **********************/
	public static $includeAllAttrsLabel = "//label[@for='vs-listgenV2-attributes__include-previous']";
	public static $attrTabFormatString = "//div[@id='%s']";
	public static $attrToggleFormatString = "//div[@id='%s__toggle']";
	public static $attrPageNextStepBtn = "//div[@id='vs-wizard__step--2']//button[@type='submit']";

	/*************** OVERVIEW ***********************/
	public static $pricingBtn = "";
	public static $viewDetailsBtn = "";
	public static $filterDetailsBtn = "";
	public static $overviewPageNextStepBtn = "//div[@id='vs-wizard__step--3']//button[@type='submit']";

  /************** MATCH CREDIT DEDUCTION **********/
	public static $authorizeDeducationLabel = "//label[@for='vs-listgenV2-debit-agree']";
	public static $orderListButton = "//button[text()='Order List']";

	/*************** RESULTS ************************/
	public static $listGenV2ResultsFinishedElem = "//div[text()='List Generation  Results']";
}