<?php

namespace Tests\Support\Page;

class DataPrep {

    # Step 0
    public static $hideIntroCheckbox = "//input[@name='hide_personaV2_intro']";
    public static $hideIntroLabel = "//input[@name='hide_personaV2_intro']/parent::label";
    public static $nextStepBtn = "//button[@type='submit']";

    # Data Prep Diagnosis Step
    // If the user was shown step 0, this will be step 4, otherwise it will be step 3. Using the more generic "submit"
    // public static $diagnosisPageNextStep = "//div[@id='vs-wizard__step--3']//button[@type='submit']";
    public static $diagnosisPageNextStep = "//button[@type='submit']";

    # Data Prep Work Area
    public static $suggestedActionsHeader = "//h1[text()='Suggested Actions']";
    public static $activeActionsHeader = "//h1[text()='Active Actions']";
    public static $dataPrepWorkAreaHeader = "//h2[text()='Data Prep Work Area']";

    public static $suggestedActionsElem = "//div[@id='vs-dataprep-suggested-actions']//div[text()='%s']";
    public static $activeActionDeleteBtn = "//div[@id='vs-dataprep-active-actions']//div[text()='%s']/following-sibling::button";

    public static $columnInput = "//div[@id='vs-dataprep-input-field-0']";
    public static $clearColumnInputBtn = "//div[text()='Input Columns']/following-sibling::div//div[@aria-hidden]";
    public static $columnInputError = "//div[text()='Missing inputs for this action.']";
    public static $actionInput = "//div[@id='vs-dataprep-selected-action']";
    public static $clearActionInputBtn = "//div[text()='Selected Action']/following-sibling::div//div[@aria-hidden]";

    # Finished
    public static $resultsElem = "//div[text()='Data Prep  Results']";
    public static $exportBtn = "//span[text()='Export']/parent::button";
    public static $exportModalText = "Download Started";
    public static $exportModalCloseBtn = "//button[@class='btn btn-primary vs-data-prep-results__rounded-12 px-4']";

}

