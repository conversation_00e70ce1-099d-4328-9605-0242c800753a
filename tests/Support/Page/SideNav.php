<?php

namespace Tests\Support\Page;

class SideNav
{
    # Menu
    public static $homeNavLink = "//a[@href='/']";
    public static $projectsLink = "//a[@href='/projects']";
    public static $personasLink = "//a[@href='/personas']";
    # Quick Links
    public static $marketingStarLink = "//a[text()='Marketing Star']";
    public static $settingsLink = "//a[@href='/account/profile']";
    public static $usageBtn = "//a[text()='REACH Usage']";
    # Help and Tutorials
    public static $faqLink = "//a[@href='https://reach-help.versium.com/docs/faq-1']";
    public static $helpLink = "//a[@href='https://reach-help.versium.com/docs/get-started']";
    public static $matchCreditInfoLink = "//a[@href='https://reach-help.versium.com/docs/match-value-conversion-table']";
    public static $apiDocsLink = "//a[@href='https://api-documentation.versium.com/']";
    public static $supportFormLink = "//a[text()='Support Form']";
    public static $howtoLink = "//a[@href='https://www.youtube.com/playlist?list=PLyAQmt2BDwgkxWlnCtYvWhRR2wx8nEV-F']";
    # Title Strings
    public static $menuNavHeader = 'Menu';
    public static $quickLinksNavHeader = 'Quick Links';
    public static $helpNavHeader = 'Help and Tutorials';

    public static $sideNavElems = null;

    public static function getSideNavElems() {
        if (self::$sideNavElems == null) {
            self::$sideNavElems = [
                # Menu
                self::$homeNavLink,
                self::$projectsLink,
                self::$personasLink,
                # Quick Links
                self::$marketingStarLink,
                self::$settingsLink,
                self::$usageBtn,
                # Help and Tutorials
                self::$faqLink,
                self::$helpLink,
                self::$matchCreditInfoLink,
                self::$apiDocsLink,
                self::$supportFormLink,
                self::$howtoLink,
            ];
        }
        return self::$sideNavElems;
    }
}
