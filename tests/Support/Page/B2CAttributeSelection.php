<?php

namespace Tests\Support\Page;

class B2CAttributeSelection {

    public static $sideHeaderString = 'Choose Output Attributes';

    public static $matchTypeLabelHH = "//label[@for='vs-b2c-filters__radio--household-match']";
    public static $matchTypeLabelINDIV = "//label[@for='vs-b2c-filters__radio--individual-match']";

    public static $priceEstimateString = 'Estimated Purchase Price';
    public static $minPriceString = 'Minimum Price';

    // contact append types
    public static $phoneCheckBoxLabel = "//div[@id='b2cPhoneAppend']//label";
    public static $emailCheckBoxLabel = "//div[@id='b2cEmailAppend']//label";
    public static $addressCheckBoxLabel = "//div[@id='b2cAddressAppend']//label";
    // phone append types
    public static $phoneBestLabel = "//label[@for='vs-b2c-filters__radio--phone_b2cPhoneAppend']";
    public static $phoneMobileLabel = "//label[@for='vs-b2c-filters__radio--phone_b2cMobilePhoneAppend']";
    public static $phoneMultiLabel = "//label[@for='vs-b2c-filters__radio--phone_b2cMultiplePhoneAppend']";
    // demographic append types
    public static $basicDemographicCheckboxLabel = "//div[@id='b2cDemographicAttributesAppend']//label";
    public static $finAutoDemographicCheckboxLabel = "//div[@id='b2cHouseFinAutoAppend']//label";
    public static $lifestyleDemographicCheckboxLabel = "//div[@id='b2cLifestyleInterestAppend']//label";
    public static $policatlDemographicCheckboxLabel = "//div[@id='b2cPoliticalDonorAppend']//label";

    public static $nextStepBtn = "//span[@id='vs-filters-and-preview__next-step']//ancestor::button";

    // Price Estimator
    public static $estPurchasePriceElem = "//div[@class='vs-price-estimate-count']//span[@class='vs-custom-count font-weight-bolder text-right h2 m-0 visible d-block']";

    // other

}
