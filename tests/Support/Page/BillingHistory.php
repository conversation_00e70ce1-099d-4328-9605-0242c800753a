<?php

namespace Tests\Support\Page;

class BillingHistory {

    public static $billingHistoryTable = "//table[@class='vs-sortable-table table mb-0 vs-billing-history-table table-borderless table-striped']";
    public static $billingHistoryActionBtn = "//tbody/tr[1]/td[@class='vs-sortable-table-cell  vs-billing-history-table__actions-cell position-relative border-left p-0  align-middle']"; // btn for first row
    public static $downloadReceiptBtn = "//button[text()='Download receipt']";

}