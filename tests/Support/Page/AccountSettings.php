<?php


namespace Tests\Support\Page;


class AccountSettings {

    // Application Settings Links
    public static $myProfileLink = "//a[@href='/account/profile']";
    public static $updatePasswordLink = "//a[@href='/account/security']";
    public static $integrationsLink = "//a[@href='/account/integrations']";
    public static $suppressionAndOptOutLink = "//a[@href='/account/suppressions']";
    public static $generalSettingsLink = "//a[@href='/account/general-settings']";
    // Payments Links
    public static $paymentDetailsLink = "//a[@href='/account/payment']";
    // Referral Credit Program Links
    public static $referralLink = "//a[@href='/account/referral-link']";
    public static $referralReportLink = "//a[@href='/account/referral-report']";

    // Suppression and Opt-Out Page
    public static $suppressionListInput = "//input[@type='file']";
    public static $suppresionListActionBtn = "//button[@class='btn btn-secondary bg-transparent text-dark border-0 rounded-0']";
    public static $exportActionBtn = "//button[text()='Export']";
    public static $renameActionBtn = "//button[text()='Rename']";
    public static $deleteActionBtn = "//button[text()='Delete']";
    public static $confirmDeleteBtn = "//div[@class='modal-footer']/button[text()='Delete']";


    private static $links = null;
    private static $actionListBtns = null;

    public static function getAccountSettingsLinks()
    {
        if (self::$links == null) {
            self::$links = [
                self::$myProfileLink,
                self::$updatePasswordLink,
                self::$integrationsLink,
                self::$suppressionAndOptOutLink,
                self::$generalSettingsLink,
                // self::$paymentDetailsLink, // subscribers dont have a payments section
                self::$referralLink,
                self::$referralReportLink,
            ];
        }
        return self::$links;
    }


    public static function getActionListBtns()
    {
        if (self::$actionListBtns == null) {
            self::$actionListBtns = [
                self::$exportActionBtn,
                self::$renameActionBtn,
                self::$deleteActionBtn
            ];
        }
        return self::$actionListBtns;
    }
}
