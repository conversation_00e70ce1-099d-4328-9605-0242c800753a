<?php
/**
 * Created by PhpStorm.
 * User: gchandramouli
 * Date: 6/5/19
 * Time: 5:27 PM
 */

namespace Tests\Support\Page;

class Project {

    //TODO: Consider renaming this page to "ProjectDashboard"

    public static $projectHeaderMessage = "//h2[contains(text(),'Select a list on the left to view List Insights.')]";

    // List Manager
    public static $listLocatorFormatString = "//div[@title='%s']";
    public static $listActionsButtonLocatorFormatString = "//a[@title='%s']/following-sibling::button";
    public static $listCardListNameElem = "//div[@class='w-100 mb-0 text-truncate text-secondary']";

    public static $actionMenuBtn = "//button[@class='vs-list-action-dropdown-toggle vt-list-action__dropdown-toggle btn btn-transparent d-flex align-items-center border-0 rounded-pill px-2 py-1 mr-2 mt-2']";
    public static $moveListActionBtn = "//span[text()='Move']/parent::button";

    // Move List Modal
    public static $moveListModalText = 'Choose where to move your list.';
    public static $moveListModalInput = "//input[@id='react-select-2-input']";
    public static $moveListModalSubmitBtn = "//button[text()='Submit']";

    // Project Name Text
    public static $projectHeaderFormatString = "//h1[contains(text(), '%s')]";
    public static $projectBreadCrumbFormatString = "//span[contains(text(), '%s')]";
}