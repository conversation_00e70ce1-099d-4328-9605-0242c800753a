<?php

namespace Tests\Support\Page;

class ChooseProject {

    public static $choseProjectHeaderString = "Keep your audiences and lists organized inside projects."; //B2C specific?
    // public static $projectNameInput = "//div[@class='vs-creatable-dropdown__input']/input";
    public static $projectNameInput = "//input[@class='vs-creatable-dropdown__input']";


    public static $projectNameInputDropDownBtn = "//input[@class='vs-creatable-dropdown__input']";
    // public static $projectNameInputDropDownBtn = "//div[@class='vs-creatable-dropdown__indicator vs-creatable-dropdown__dropdown-indicator css-tlfecz-indicatorContainer']";
    public static $selectedProjectNameElem = "//div[contains(@class, 'vs-creatable-dropdown__single-value')]";
    // public static $selectedProjectNameElem = "//div[@class='vs-creatable-dropdown__single-value css-1uccc91-singleValue']";

    public static $projectNameOptionFormatString = "//div[contains(text(),'%s')]";
    public static $createButton = "//section[@class='vs-choose-campaign container']//button[contains(.,'Create')]";

    // Price Estimator
    public static $estPurchasePriceElem = "//div[@class='vs-price-estimate-count']//span[@class='vs-custom-count font-weight-bolder text-right h2 m-0 visible d-block']";
}