<?php

namespace Tests\Support\Page\Versium\Resources;

class WebinarsPage {

    public static $notifyMeLink = "//a[contains(@href, '/webinars-signup')]"; //currently broken on staging
    public static $webinarLink1 = "//a[contains(@href, '/webinar/b2b2c-identity-graphing-looking-beyond-cookies-covid')]";
    public static $webinarLink2 = "//a[contains(@href, '/webinar/versium-reach-for-b2b-demo')]";
    public static $webinarLink3 = "//a[contains(@href, '/webinar/marketing-roi')]";
    public static $webinarLink4 = "//a[contains(@href, '/webinar/supercharge-dynamics-365')]";
    public static $webinarLink5 = "//a[contains(@href, '/webinar/profitability')]";


    public static $links = [];

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$notifyMeLink,
                self::$webinarLink1,
                self::$webinarLink2,
                self::$webinarLink3,
                self::$webinarLink4,
                self::$webinarLink5,
            ];
        }
        return self::$links;
    }
}