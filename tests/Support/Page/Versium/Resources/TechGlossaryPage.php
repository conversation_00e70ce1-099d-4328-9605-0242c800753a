<?php

namespace Tests\Support\Page\Versium\Resources;

class TechGlossaryPage {

    public static $predictiveModelingLink = "//a[contains(@href, '/tech-glossary-term/predictive-modeling')]";
    public static $b2cMarketingLink = "//a[contains(@href, '/tech-glossary-term/b2c-marketing')]";
    public static $onlineTargetingLink = "//a[contains(@href, '/tech-glossary-term/online-targeting')]";
    public static $socMediaMarketingLink = "//a[contains(@href, '/tech-glossary-term/social-media-marketing')]";
    public static $seoLink = "//a[contains(@href, '/tech-glossary-term/search-engine-optimization')]";
    public static $semLink = "//a[contains(@href, '/tech-glossary-term/search-engine-marketing')]";
    public static $saasLink = "//a[contains(@href, '/tech-glossary-term/saas')]";
    public static $onlineReachLink = "//a[contains(@href, '/tech-glossary-term/online-reach')]";
    public static $omniChannelLink = "//a[contains(@href, '/tech-glossary-term/omni-channel-marketing')]";
    public static $onlineAudienceLink = "//a[contains(@href, '/tech-glossary-term/online-audience')]";
    public static $pdaLink = "//a[contains(@href, '/tech-glossary-term/programmatic-display-ads')]";
    public static $predictiveScoresLink = "//a[contains(@href, '/tech-glossary-term/predictive-scores')]";
    public static $predictiveAnalytics = "//a[contains(@href, '/tech-glossary-term/predictive-analytics')]";
    public static $personaListGenLink = "//a[contains(@href, '/tech-glossary-term/persona-list-gen')]";
    public static $personabasedMarketingLink = "//a[contains(@href, '/tech-glossary-term/persona-based-marketing')]";
    public static $multiChannelMarketingLink = "//a[contains(@href, '/tech-glossary-term/multi-channel-marketing')]";
    public static $marketingAutomationLink = "//a[contains(@href, '/tech-glossary-term/marketing-automation')]";
    public static $marketingAudienceLink = "//a[contains(@href, '/tech-glossary-term/marketing-audience')]";
    public static $marketingAnalyticsLink = "//a[contains(@href, '/tech-glossary-term/marketing-analytics')]";
    public static $machineLearningLink = "//a[contains(@href, '/tech-glossary-term/machine-learning')]";
    public static $lookalikeLink = "//a[contains(@href, '/tech-glossary-term/look-alike-audience')]";
    public static $linkedInMarketingLink = "//a[contains(@href, '/tech-glossary-term/linkedin-marketing')]";
    public static $identityGraphLink = "//a[contains(@href, '/tech-glossary-term/identity-graph')]";
    public static $facebookMarketingLink = "//a[contains(@href, '/tech-glossary-term/facebook-marketing')]";
    public static $emailMarkingLink = "//a[contains(@href, '/tech-glossary-term/email-marketing-list')]";
    public static $demandSideLink = "//a[contains(@href, '/tech-glossary-term/demand-side-platform')]";
    public static $dataScienceLink = "//a[contains(@href, '/tech-glossary-term/data-science')]";
    public static $dataManagementPlatformLink = "//a[contains(@href, '/tech-glossary-term/data-management-platform')]";
    public static $dataHygieneLink = "//a[contains(@href, '/tech-glossary-term/data-hygiene')]";
    public static $daasLink = "//a[contains(@href, '/tech-glossary-term/daas')]";
    public static $customerIdentityLink = "//a[contains(@href, '/tech-glossary-term/customer-identity')]";
    public static $conversionRateLink = "//a[contains(@href, '/tech-glossary-term/conversion-rate')]";
    public static $cookieMatchingLink = "//a[contains(@href, '/tech-glossary-term/cookie-matching')]";
    public static $b2b2cMarketingLink = "//a[contains(@href, '/tech-glossary-term/b2b2c-marketing')]";
    public static $b2bMarketingLink = "//a[contains(@href, '/tech-glossary-term/b2b-marketing')]";
    public static $aiLink = "//a[contains(@href, '/tech-glossary-term/artificial-intelligence')]";
    public static $abmLink = "//a[contains(@href, '/tech-glossary-term/account-based-marketing')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$predictiveModelingLink,
                self::$b2cMarketingLink,
                self::$onlineTargetingLink,
                self::$socMediaMarketingLink,
                self::$seoLink,
                self::$semLink,
                self::$saasLink,
                self::$onlineReachLink,
                self::$omniChannelLink,
                self::$onlineAudienceLink,
                self::$pdaLink,
                self::$predictiveScoresLink,
                self::$predictiveAnalytics,
                self::$personaListGenLink,
                self::$personabasedMarketingLink,
                self::$multiChannelMarketingLink,
                self::$marketingAutomationLink,
                self::$marketingAudienceLink,
                self::$marketingAnalyticsLink,
                self::$machineLearningLink,
                self::$lookalikeLink,
                self::$linkedInMarketingLink,
                self::$identityGraphLink,
                self::$facebookMarketingLink,
                self::$emailMarkingLink,
                self::$demandSideLink,
                self::$dataScienceLink,
                self::$dataManagementPlatformLink,
                self::$dataHygieneLink,
                self::$daasLink,
                self::$customerIdentityLink,
                self::$conversionRateLink,
                self::$cookieMatchingLink,
                self::$b2b2cMarketingLink,
                self::$b2bMarketingLink,
                self::$aiLink,
                self::$abmLink,
            ];
        }
        return self::$links;
    }
}