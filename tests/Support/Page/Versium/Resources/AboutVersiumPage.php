<?php

namespace Tests\Support\Page\Versium\Resources;

class AboutVersiumPage {

    public static $startFreeTrialLink1 = "//div[@data-id='d5c37f9']//a[contains(@href, '/choose-trial')]";
    public static $tryItFreeLink = "//div[@data-id='5cabebc']//a[contains(@href, '/choose-trial')]";
    public static $visitWebsiteLink = "//a[contains(@href, 'https://datafinder.com/?')]";
    public static $contactUsLink = "//div[@data-id='b71b2df']//a[contains(@href, '/contact-form')]";
    public static $learnMoreLink1 = "//div[@data-id='f18d5f7']//a[contains(@href, '/versium-predict')]";
    public static $learnMoreLink2 = "//div[@data-id='dc09cfb']//a[contains(@href, 'api-documentation.versium.com')]";
    public static $startFreeTrialLink2 = "//div[@data-id='7ec22183']//a[contains(@href, '/choose-trial')]";
    public static $joinTheTeamLink = "//div[@data-id='05e39ce']//a[contains(@href, '/careers')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$startFreeTrialLink1,
                self::$tryItFreeLink,
                self::$visitWebsiteLink,
                self::$contactUsLink,
                self::$learnMoreLink1,
                self::$learnMoreLink2,
                self::$startFreeTrialLink2,
                self::$joinTheTeamLink,
            ];
        }
        return self::$links;
    }
}