<?php

namespace Tests\Support\Page\Versium\Resources;

class CaseStudiesPage {

    public static $tryItYourselfLink = "//div[@data-id='b02f730']//a[contains(@href, '/choose-trial')]";
    public static $caseStudyLink1 = "//a[contains(@href, '/case-study/socially-responsible-employee-benefits-company-launches-abm-strategy-with-versium-reach')]";
    public static $caseStudyLink2 = "//a[contains(@href, '/case-study/hr-saas-company')]";
    public static $caseStudyLink3 = "//a[contains(@href, '/case-study/saas-company')]";
    public static $caseStudyLink4 = "//a[contains(@href, '/case-study/outerwall')]";
    public static $caseStudyLink5 = "//a[contains(@href, '/case-study/online-university')]";
    public static $caseStudyLink6 = "//a[contains(@href, '/case-study/t-mobile')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$tryItYourselfLink,
                self::$caseStudyLink1,
                self::$caseStudyLink2,
                self::$caseStudyLink3,
                self::$caseStudyLink4,
                self::$caseStudyLink5,
                self::$caseStudyLink6,
            ];
        }
        return self::$links;
    }
}