<?php

namespace Tests\Support\Page\Versium\Resources;

class VersiumBlogPage {

    public static $subscribeLink = "//a[contains(@href, '/newsletter-signup')]";
    # blog post links
    public static $skipTracingBlogLink =  "//a[contains(@href, '/why-you-should-care-about-skip-tracing')]";
    public static $fourHacksBlogLink = "//a[contains(@href, '/blog/4-hacks-to-get-the-results-you-want-from-b2b-marketing')]";
    public static $skipTracingRealEstateBlogLink = "//a[contains(@href, '/blog/how-to-use-skip-tracing-for-real-estate-investors')]";
    public static $identityMappingBlogLink = "//a[contains(@href, '/blog/identity-mapping-technology-revolutionizing-consumer-marketing-reach')]";
    public static $bigPredictionBlogLink = "//a[contains(@href, '/blog/the-big-2021-prediction-for-b2b-marketing')]";
    public static $customerContactListsBlogLink = "//a[contains(@href, '/blog/how-to-use-customer-contact-lists-for-online-audiences')]";
    public static $dataEnrichmentBlogLink = "//a[contains(@href, '/blog/how-to-use-data-enrichment-for-b2c-marketing')]";
    public static $wakeOfCovidBlogLink = "//a[contains(@href, '/blog/how-to-connect-with-b2b-audiences-online-in-the-wake-of-covid-19-a-world-thats-working-from-home-and-engaged-online')]";
    public static $gettingStartedAbmBlogLink = "//a[contains(@href, '/blog/one-tool-to-empower-b2b-companies-getting-started-with-abm')]";
    public static $sixElementsBlogLink = "//a[contains(@href, '/blog/6-important-elements-to-look-for-in-an-identity-graph')]";
    public static $improveMatchRatesBlogLink = "//a[contains(@href, '/blog/how-to-improve-match-rates-with-facebook-custom-audiences')]";
    public static $five2020PredictionsBlogLink = "//a[contains(@href, '/blog/five-2020-predictions-for-b2b-marketers')]";
    public static $knowYourAudienceBlogLink = "//a[contains(@href, '/blog/how-well-do-you-know-your-audience-get-your-insights')]";
    public static $targetingLikeABossBlogLink = "//a[contains(@href, '/blog/targeting-like-a-boss')]";
    public static $qAndAWithJoeBlogLink = "//a[contains(@href, '/blog/qa-with-joe-hafner-seattle-hug-lead')]";
    public static $succeedAtHubspotBlogLink = "//a[contains(@href, '/blog/how-to-succeed-at-hubspot-inbound-2019')]";
    public static $consumerDataBlogLink = "//a[contains(@href, '/blog/consumer-data')]";
    public static $theTruthBlogLink = "//a[contains(@href, '/blog/the-truth-about-building-marketing-audiences-in-advertising-platforms')]";
    public static $omniChannelExplainedBlogLink = "//a[contains(@href, '/blog/omni-channel-marketing-and-multi-channel-marketing-explained')]";
    public static $identityGraphBlogLink = "//a[contains(@href, '/blog/meet-your-new-best-friend-the-versium-identity-graph')]";
    public static $nonIntrusiveMarketingBlogLink = "//a[contains(@href, '/blog/the-secret-to-non-intrusive-marketing')]";
    public static $theyreHumansBlogLink = "//a[contains(@href, '/blog/theyre-not-targets-theyre-human')]";
    public static $directEmailBlogLink = "//a[contains(@href, '/blog/best-practices-for-direct-email-campaigns')]";


    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$subscribeLink,
                self::$skipTracingBlogLink,
                self::$fourHacksBlogLink,
                self::$skipTracingRealEstateBlogLink,
                self::$identityMappingBlogLink,
                self::$bigPredictionBlogLink,
                self::$customerContactListsBlogLink,
                self::$dataEnrichmentBlogLink,
                self::$wakeOfCovidBlogLink,
                self::$gettingStartedAbmBlogLink,
                self::$sixElementsBlogLink,
                self::$improveMatchRatesBlogLink,
                self::$five2020PredictionsBlogLink,
                self::$knowYourAudienceBlogLink,
                self::$targetingLikeABossBlogLink,
                self::$qAndAWithJoeBlogLink,
                self::$succeedAtHubspotBlogLink,
                self::$consumerDataBlogLink,
                self::$theTruthBlogLink,
                self::$omniChannelExplainedBlogLink,
                self::$identityGraphBlogLink,
                self::$nonIntrusiveMarketingBlogLink,
                self::$theyreHumansBlogLink,
                self::$directEmailBlogLink,
            ];
        }
        return self::$links;
    }
}
