<?php

namespace Tests\Support\Page\Versium\Pricing;

class B2cPricingPage {

    public static $startFreeTrialLink1 = "//div[@data-id='df49cb8']//a[contains(@href, '/b2c-signup')]";
    public static $choosePricingLink1 = "//div[@data-id='55929c1']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink2 = "//div[@data-id='c24b6d1']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink3 = "//div[@data-id='e95a83c']//a[contains(@href, '/b2c-plan-request')]";
    public static $contactSalesLink1 = "//div[@data-id='14b5ae8']//a[contains(@href, '/b2c-plan-request')]";
    public static $contactSalesLink2 = "//div[@data-id='da539e9']//a[contains(@href, '/b2c-plan-request')]";
    public static $contactSalesLink3 = "//div[@data-id='bdb1dfa']//a[contains(@href, '/b2c-plan-request')]";
    public static $contactSalesLink4 = "//div[@data-id='e1ea7c1']//a[contains(@href, '/b2c-plan-request')]";
    public static $contactUsLink = "//div[@data-id='cd5d50a']//a[contains(@href, '/contact-form')]";
    public static $getStartedNowLink = "//div[@data-id='59f2aa2']//a[contains(@href, '/b2c-signup')]";


    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$startFreeTrialLink1,
                self::$choosePricingLink1,
                self::$choosePricingLink2,
                self::$choosePricingLink3,
                self::$contactSalesLink1,
                self::$contactSalesLink2,
                self::$contactSalesLink3,
                self::$contactSalesLink4,
                self::$contactUsLink,
                self::$getStartedNowLink,
            ];
        }
        return self::$links;
    }

}