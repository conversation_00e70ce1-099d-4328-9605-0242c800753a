<?php

namespace Tests\Support\Page\Versium\Pricing;

class B2bPricingPage {

    public static $startFreeTrialLink1 = "//div[@data-id='88ac301']//a[contains(@href, '/b2b-signup')]";
    public static $choosePricingLink1 = "//div[@data-id='a7a4766']//a[contains(@href, '/b2b-plan-request')]";
    public static $choosePricingLink2 = "//div[@data-id='a8fc4a1']//a[contains(@href, '/b2b-plan-request')]";
    public static $choosePricingLink3 = "//div[@data-id='b9a6d90']//a[contains(@href, '/b2b-plan-request')]";
    public static $contactSalesLink1 = "//div[@data-id='8726cba']//a[contains(@href, '/b2b-plan-request')]";
    public static $contactSalesLink2 = "//div[@data-id='be92541']//a[contains(@href, '/b2b-plan-request')]";
    public static $contactSalesLink3 = "//div[@data-id='596d189']//a[contains(@href, '/b2b-plan-request')]";
    public static $contactUsLink = "//div[@data-id='7a6c3a5']//a[contains(@href, '/contact-form')]";
    public static $getStartedNowLink = "//div[@data-id='b632464']//a[contains(@href, '/b2b-signup')]";


    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$startFreeTrialLink1,
                self::$choosePricingLink1,
                self::$choosePricingLink2,
                self::$choosePricingLink3,
                self::$contactSalesLink1,
                self::$contactSalesLink2,
                self::$contactSalesLink3,
                self::$contactUsLink,
                self::$getStartedNowLink,
            ];
        }
        return self::$links;
    }

}