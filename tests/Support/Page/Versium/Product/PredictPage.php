<?php

namespace Tests\Support\Page\Versium\Product;

class PredictPage {

    public static $learnMoreLink1 = "//div[@data-id='b926551']//a[@href='https://appsource.microsoft.com/en-us/product/dynamics-365/versium-analytics.e74ab7ae-bf54-4561-9e76-373c1a66016d']";
    public static $blogPostLink = "//a[contains(@href, '/blog/meet-your-new-best-friend-the-versium-identity-graph')]";
    public static $learnMoreLink2 = "//div[@data-id='54a8a0e']//a[@href='https://appsource.microsoft.com/en-us/product/dynamics-365/versium-analytics.e74ab7ae-bf54-4561-9e76-373c1a66016d']";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$learnMoreLink1,
                self::$blogPostLink,
                self::$learnMoreLink2,
            ];
        }
        return self::$links;
    }

}