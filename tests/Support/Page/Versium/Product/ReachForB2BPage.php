<?php

namespace Tests\Support\Page\Versium\Product;

use AcceptanceTester;

class ReachForB2BPage {

    public static $startFreeTrialLink1 = "//div[@data-id='133d6857']//a[contains(@href, '/choose-trial')]";
    public static $startFreeTrialLink2 = "//div[@data-id='60b852b5']//a[contains(@href, '/choose-trial')]";
    public static $viewAllCaseStudiesLink = "//div[@data-id='dd2f130']//a[contains(@href, '/case-studies')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$startFreeTrialLink1,
                self::$startFreeTrialLink2,
                self::$viewAllCaseStudiesLink,
            ];
        }
        return self::$links;
    }

}