<?php

namespace Tests\Support\Page\Versium\Product;

class ReachForB2CPage {

    public static $startFreeTrialLink1 = "//div[@data-id='5a34521c']//a[contains(@href, 'choose-trial')]";
    public static $startFreeTrialLink2 = "//div[@data-id='40621f3d']//a[contains(@href, 'choose-trial')]";
    public static $viewAllCaseStudiesLink = "//div[@data-id='62abb073']//a[contains(@href, '/case-studies')]";

    // these links are displayed conditionally based on what tab is selected
    public static $learnMoreLink1 = "//a[contains(@href, '/reach-b2c/consumer-list-insights')]";
    public static $learnMoreLink2 = "//a[contains(@href, '/reach-b2c/consumer-contact-append')]";

    // tab
    public static $consumerContactAppendTab = "//div[@id='elementor-tab-title-9663']";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$startFreeTrialLink1,
                self::$startFreeTrialLink2,
                self::$viewAllCaseStudiesLink,
                self::$learnMoreLink1
            ];
        }
        return self::$links;
    }
}