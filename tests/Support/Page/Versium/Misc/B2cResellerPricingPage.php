<?php

namespace Tests\Support\Page\Versium\Misc;

class B2cResellerPricingPage {

    public static $contactUsLink1 = "//div[@data-id='1115ac39']//a[contains(@href, '/contact-form')]";
    public static $choosePricingLink1 = "//div[@data-id='3506fb72']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink2 = "//div[@data-id='393986c0']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink3 = "//div[@data-id='c35db2b']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink4 = "//div[@data-id='25418e55']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink5 = "//div[@data-id='36812cc4']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink6 = "//div[@data-id='7ac9bc3b']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink7 = "//div[@data-id='be98517']//a[contains(@href, '/b2c-plan-request')]";
    public static $contactUsLink2 = "//div[@data-id='41d401de']//a[contains(@href, '/contact-form')]";
    public static $startFreeTrialLink = "//div[@data-id='7e6e1019']//a[contains(@href, '/choose-trial')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$contactUsLink1,
                self::$choosePricingLink1,
                self::$choosePricingLink2,
                self::$choosePricingLink3,
                self::$choosePricingLink4,
                self::$choosePricingLink5,
                self::$choosePricingLink6,
                self::$choosePricingLink7,
                self::$contactUsLink2,
                self::$startFreeTrialLink
            ];
        }
        return self::$links;
    }
}