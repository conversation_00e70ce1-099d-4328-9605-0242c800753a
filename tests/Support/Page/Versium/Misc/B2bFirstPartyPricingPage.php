<?php

namespace Tests\Support\Page\Versium\Misc;

class B2bFirstPartyPricingPage {

    public static $contactUsLink1 = "//div[@data-id='2266adbc']//a[contains(@href, '/contact-form')]";
    public static $choosePricingLink1 = "//div[@data-id='3e2446cc']//a[contains(@href, '/b2b-plan-request')]";
    public static $choosePricingLink2 = "//div[@data-id='630398dc']//a[contains(@href, '/b2b-plan-request')]";
    public static $choosePricingLink3 = "//div[@data-id='67e83f72']//a[contains(@href, '/b2b-plan-request')]";
    public static $choosePricingLink4 = "//div[@data-id='66a11c14']//a[contains(@href, '/b2b-plan-request')]";
    public static $choosePricingLink5 = "//div[@data-id='dce49ae']//a[contains(@href, '/b2b-plan-request')]";
    public static $choosePricingLink6 = "//div[@data-id='5b5814fc']//a[contains(@href, '/b2b-plan-request')]";
    public static $choosePricingLink7 = "//div[@data-id='2f0057ae']//a[contains(@href, '/b2b-plan-request')]";
    public static $contactUsLink2 = "//div[@data-id='6f24bae5']//a[contains(@href, '/contact-form')]";
    public static $startFreeTrialLink = "//div[@data-id='5e21fe7f']//a[contains(@href, '/choose-trial')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$contactUsLink1,
                self::$choosePricingLink1,
                self::$choosePricingLink2,
                self::$choosePricingLink3,
                self::$choosePricingLink4,
                self::$choosePricingLink5,
                self::$choosePricingLink6,
                self::$choosePricingLink7,
                self::$contactUsLink2,
                self::$startFreeTrialLink
            ];
        }
        return self::$links;
    }
}