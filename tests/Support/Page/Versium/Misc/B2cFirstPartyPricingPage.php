<?php

namespace Tests\Support\Page\Versium\Misc;

Class B2cFirstPartyPricingPage {

    public static $contactUsLink1 = "//div[@data-id='3835cc9a']//a[contains(@href, '/contact-form')]";
    public static $choosePricingLink1 = "//div[@data-id='2b5b6e3a']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink2 = "//div[@data-id='6a5cdb1c']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink3 = "//div[@data-id='4eff99a4']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink4 = "//div[@data-id='24a8ddc']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink5 = "//div[@data-id='c1be692']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink6 = "//div[@data-id='50aeb8f']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink7 = "//div[@data-id='d5ec0db']//a[contains(@href, '/b2c-plan-request')]";
    public static $contactUsLink2 = "//div[@data-id='78c23701']//a[contains(@href, '/contact-form')]";
    public static $startFreeTrialLink = "//div[@data-id='5c1a3385']//a[contains(@href, '/choose-trial')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$contactUsLink1,
                self::$choosePricingLink1,
                self::$choosePricingLink2,
                self::$choosePricingLink3,
                self::$choosePricingLink4,
                self::$choosePricingLink5,
                self::$choosePricingLink6,
                self::$choosePricingLink7,
                self::$contactUsLink2,
                self::$startFreeTrialLink
            ];
        }
        return self::$links;
    }
}