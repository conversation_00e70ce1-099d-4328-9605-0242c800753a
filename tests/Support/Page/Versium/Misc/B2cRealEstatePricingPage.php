<?php

namespace Tests\Support\Page\Versium\Misc;

class B2cRealEstatePricingPage {

    public static $contactUsLink1 = "//div[@data-id='7f0c355e']//a[contains(@href, '/contact-form')]";
    public static $choosePricingLink1 = "//div[@data-id='77374ee8']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink2 = "//div[@data-id='56daffde']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink3 = "//div[@data-id='7e8a4fba']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink4 = "//div[@data-id='11dbdabc']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink5 = "//div[@data-id='8334ca0']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink6 = "//div[@data-id='63e39f57']//a[contains(@href, '/b2c-plan-request')]";
    public static $choosePricingLink7 = "//div[@data-id='267d9d81']//a[contains(@href, '/b2c-plan-request')]";
    public static $contactUsLink2 = "//div[@data-id='4ae48ffb']//a[contains(@href, '/contact-form')]";
    public static $startFreeTrialLink = "//div[@data-id='215ef9e3']//a[contains(@href, '/choose-trial')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$contactUsLink1,
                self::$choosePricingLink1,
                self::$choosePricingLink2,
                self::$choosePricingLink3,
                self::$choosePricingLink4,
                self::$choosePricingLink5,
                self::$choosePricingLink6,
                self::$choosePricingLink7,
                self::$contactUsLink2,
                self::$startFreeTrialLink
            ];
        }
        return self::$links;
    }
}