<?php

namespace Tests\Support\Page\Versium;

class Footer {

    # Links: About
    public static $aboutUsFooterLink = "//ul[@id='menu-1-840e460']//a[contains(@href, '/about-versium')][text()='About Versium']";
    public static $whyVersiumFooterLink = "//ul[@id='menu-1-840e460']//a[contains(@href, '/why-versium')]";
    public static $ourProductsFooterLink = "//ul[@id='menu-1-840e460']//a[contains(@href, '/our-products')]";
    public static $careersFooterLink = "//ul[@id='menu-1-840e460']//a[contains(@href, '/careers')]";
    # Links: REACH
    public static $reachForB2bFooterLink = "//ul[@id='menu-1-e7670a3']//a[text()='REACH B2B']";
    public static $reachForB2cFooterLink = "//ul[@id='menu-1-e7670a3']//a[text()='REACH for B2C']";
    public static $b2bPricingFooterLink = "//ul[@id='menu-1-e7670a3']//a[text()='B2B Pricing']";
    public static $b2cPricingFooterLink = "//ul[@id='menu-1-e7670a3']//a[text()='B2C Pricing']";
    public static $requestTrialFooterLink = "//ul[@id='menu-1-e7670a3']//a[text()='Request Trial']";
    public static $loginFooterLink = "//ul[@id='menu-1-e7670a3']//a[text()='Login']";
    # Links: Resources
    public static $webinarsFooterLink = "//ul[@id='menu-1-a574876']//a[text()='Webinars']";
    public static $caseStudiesFooterLink = "//ul[@id='menu-1-a574876']//a[text()='Case Studies']";
    public static $techGlossayFooterLink = "//ul[@id='menu-1-a574876']//a[text()='Tech Glossary']";
    public static $versiumBlogFooterLink = "//ul[@id='menu-1-a574876']//a[text()='Versium Blog']";
    public static $newsroomFooterLink = "//ul[@id='menu-1-a574876']//a[text()='Newsroom']";
    # Links: Support
    public static $contactUsFooterLink = "//ul[@id='menu-1-77bac88']//a[text()='Contact Us']";
    public static $supportFormFooterLink = "//ul[@id='menu-1-77bac88']//a[text()='Support Form']";
    public static $viewFAQsFooterLink = "//ul[@id='menu-1-77bac88']//a[text()='FAQs']";
    public static $requestTrialSupportFooterLink = "//ul[@id='menu-1-77bac88']//a[text()='Request Trial']";

}