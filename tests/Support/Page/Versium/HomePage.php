<?php

namespace Tests\Support\Page\Versium;

class HomePage {

    # SEO Tags
    public static $seoDescriptionTag = "//meta[@name='description']";
    public static $seoOgLocalTag = "//meta[@property='og:locale']";
    public static $seoOgTypeTag = "//meta[@property='og:type']";
    public static $seoOgTitleTag = "//meta[@property='og:title']";
    public static $seoOgDescriptionTag = "//meta[@property='og:description']";
    public static $seoOgUrlTag = "//meta[@property='og:url']";
    public static $seoOgSiteNameTag = "//meta[@property='og:site_name']";
    public static $seoTwitterCardTag = "//meta[@name='twitter:card']";
    public static $seoTwitterTitleTag = "//meta[@name='twitter:title']";
    public static $seoTwitterDescriptionTag = "//meta[@name='twitter:description']";

    # Links
    public static $acceptCookiesBtn = "//a[@id='vs-cookie-accept-btn']";

    public static $learnMoreLink1 = "//a[contains(@href, '/why-versium')][@class='elementor-button-link elementor-button elementor-size-sm elementor-animation-float']"; //updated for elementor
    public static $learnMoreLink2 = "//a[contains(@href, '/reach-b2b')][@class='elementor-button-link elementor-button elementor-size-sm elementor-animation-float']";
    public static $startFreeTrialLink1 = "//div[@data-id='b60e7a3']//a[contains(@href, '/choose-trial')]";
    public static $learnMoreLink3 = "//div[@data-id='be85e20']//a[contains(@href, '/blog/omni-channel-marketing-and-multi-channel-marketing-explained')]";
    public static $learnMoreLink4 = "//div[@data-id='dc1440b']//a[contains(@href, '/solutions/b2b-paid-advertising')]";
    public static $learnMoreLink5 = "//div[@data-id='7817b19']//a[contains(@href, '/blog/omni-channel-marketing-and-multi-channel-marketing-explained')]";
    public static $learnMoreLink6 = "//div[@data-id='a156486']//a[contains(@href, '/solutions/account-based-marketing')]";
    public static $viewCaseStudiesLink = "//div[@data-id='2bea8c5']//a[contains(@href, '/case-studies')]";
    public static $startFreeTrialLink2 = "//div[@data-id='70155a40']//a[contains(@href, '/choose-trial')]";

    # Elements
    public static $footerElem = "//section[@data-id='4521d94']";

    # Chat Widget
    public static $chatWidgetiFrame = "//iframe[@id='insent-iframe']";
    public static $chatWidget = "//div[@id='insent-chat-widget']";
    public static $chatWidgetGreeting = "//div[text()='How would you like to proceed?']";
    public static $chatWidgetClosebtn = "//div[@id='insent-card-close']";


    public static $seoTags = null;
    public static $links = null;

    public static function getSEOTags() {
        if (self::$seoTags == null) {
            self::$seoTags = [
                self::$seoDescriptionTag,
                self::$seoOgLocalTag,
                self::$seoOgTypeTag,
                self::$seoOgTitleTag,
                self::$seoOgDescriptionTag,
                self::$seoOgUrlTag,
                self::$seoOgSiteNameTag,
                self::$seoTwitterCardTag,
                self::$seoTwitterTitleTag,
                self::$seoTwitterDescriptionTag
            ];
        }
        return self::$seoTags;
    }

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$learnMoreLink1,
                self::$learnMoreLink2,
                self::$startFreeTrialLink1,
                self::$learnMoreLink3,
                self::$learnMoreLink4,
                self::$learnMoreLink5,
                self::$learnMoreLink6,
                self::$viewCaseStudiesLink,
                self::$startFreeTrialLink2
            ];
        }
        return self::$links;
    }

}