<?php

namespace Tests\Support\Page\Versium\Solutions;

class PaidAdvertisingPage {

    public static $learnMoreLink = "//div[@data-id='42fd148b']//a[contains(@href, '#')]";
    public static $startFreeTrialLink2 = "//div[@data-id='05a2bfa']//a[contains(@href, '/choose-trial')]";
    public static $startFreeTrialLink3 = "//div[@data-id='c400884']//a[contains(@href, '/choose-trial')]";
    public static $learnMoreLink1 = "//a[contains(@href, '/blog/the-truth-about-building-marketing-audiences-in-advertising-platforms')]";
    public static $watchTheVideoLink = "//a[contains(@href, 'https://www.youtube.com/watch?v=FH1YGC0kQyw')]";
    public static $learnMoreLink2 = "//a[contains(@href, '/blog/consumer-data')]";
    public static $getTheGuideLink = "//a[@href='https://info.versium.com/ultimate-2020-b2b-marketing-guide']";
    public static $learnMoreLink3 = "//a[contains(@href, '/blog/how-to-improve-match-rates-with-facebook-custom-audiences')]";
    public static $startFreeTrialLink4 = "//div[@data-id='56f132c0']//a[contains(@href, '/choose-trial')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$learnMoreLink,
                self::$startFreeTrialLink2,
                self::$startFreeTrialLink3,
                self::$learnMoreLink1,
                self::$watchTheVideoLink,
                self::$learnMoreLink2,
                self::$getTheGuideLink,
                self::$learnMoreLink3,
                self::$startFreeTrialLink4,
            ];
        }
        return self::$links;
    }
}