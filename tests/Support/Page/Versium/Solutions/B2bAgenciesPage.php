<?php

namespace Tests\Support\Page\Versium\Solutions;

class B2bAgenciesPage {

    public static $startFreeTrialLink1 = "//div[@data-id='19c6f557']//a[contains(@href, '/choose-trial')]";
    public static $startFreeTrialLink2 = "//div[@data-id='8250b8a']//a[contains(@href, '/choose-trial')]";
    public static $startFreeTrialLink3 = "//div[@data-id='0a7f6f5']//a[contains(@href, '/choose-trial')]";
    public static $learnMoreLink1 = "//a[contains(@href, '/blog/the-truth-about-building-marketing-audiences-in-advertising-platforms')]";
    public static $watchTheVideoLink = "//a[@href='https://youtu.be/FH1YGC0kQyw']";
    public static $learnMoreLink2 = "//a[contains(@href, '/blog/consumer-data')]";
    public static $getTheGuideLink2 = "//a[contains(@href, '/ultimate-b2b-marketing-guide')]";
    public static $learnMoreLink3 = "//a[contains(@href, '/blog/how-to-improve-match-rates-with-facebook-custom-audiences')]";
    public static $startFreeTrialLink4 = "//div[@data-id='fa87745']//a[contains(@href, '/choose-trial')]";
    public static $startFreeTrialLink5 = "//div[@data-id='c69a4b3']//a[contains(@href, '/choose-trial')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$startFreeTrialLink1,
                self::$startFreeTrialLink2,
                self::$startFreeTrialLink3,
                self::$learnMoreLink1,
                self::$watchTheVideoLink,
                self::$learnMoreLink2,
                self::$getTheGuideLink2,
                self::$learnMoreLink3,
                self::$startFreeTrialLink4,
                self::$startFreeTrialLink5,
            ];
        }
        return self::$links;
    }
}