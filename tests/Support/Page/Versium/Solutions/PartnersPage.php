<?php

namespace Tests\Support\Page\Versium\Solutions;

class PartnersPage {

    public static $becomeAPartnerLink = "//a[@href='#partner-signup']";
    public static $caseStudyLink1 = "//section[contains(@data-wts-url, '/case-study/socially-responsible-employee-benefits-company-launches-abm-strategy-with-versium-reach')]";
    public static $caseStudyLink2 = "//section[contains(@data-wts-url, '/case-study/hr-saas-company')]";
    public static $caseStudyLink3 = "//section[contains(@data-wts-url, '/case-study/saas-company')]";
    public static $viewAllCaseStudiesLink = "//div[@data-id='fc14d3b']//a[contains(@href, '/case-studies')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$becomeAPartnerLink,
                self::$caseStudyLink1,
                self::$caseStudyLink2,
                self::$caseStudyLink3,
                self::$viewAllCaseStudiesLink,
            ];
        }
        return self::$links;
    }
}