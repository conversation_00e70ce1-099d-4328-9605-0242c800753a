<?php

namespace Tests\Support\Page\Versium\Solutions;

class AccountBasedMarketingPage {

    public static $startFreeTrialLink1 = "//div[@data-id='7f567ea9']//a[contains(@href, '/choose-trial')]";
    public static $startFreeTrialLink2 = "//div[@data-id='9595e4e']//a[contains(@href, '/choose-trial')]";
    public static $startFreeTrialLink3 = "//div[@data-id='b1de840']//a[contains(@href, '/choose-trial')]";
    public static $learnMoreLink1 = "//a[contains(@href, '/slider/account-based-marketing')]";
    public static $watchTheVideoLink = "//a[@href='https://youtu.be/vL6CV80ELwU']";
    public static $getTheGuideLink = "//a[@href='https://info.versium.com/ultimate-b2b-marketing-guide']";
    public static $startFreeTrialLink4 = "//div[@data-id='8bec4a0']//a[contains(@href, '/choose-trial')]";

    public static $links = null;

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = [
                self::$startFreeTrialLink1,
                self::$startFreeTrialLink2,
                self::$startFreeTrialLink3,
                self::$learnMoreLink1,
                self::$watchTheVideoLink,
                self::$getTheGuideLink,
                self::$startFreeTrialLink4,
            ];
        }
        return self::$links;
    }
}