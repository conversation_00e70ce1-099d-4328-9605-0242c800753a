<?php

namespace Tests\Support\Page\Versium;

class NavBar {

    # Links: Top Level
    public static $versiumHomeLogo = "//a[contains(@href, 'versium.com')]/img";
    public static $whyVersiumLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/why-versium')][text()='Why Versium?']";
    public static $productLink = "//ul[@id='menu-1-4dddc6e']//a[text()='Product']";
    public static $pricingLink = "//ul[@id='menu-1-4dddc6e']//a[text()='Pricing']";
    public static $resourcesLink = "//ul[@id='menu-1-4dddc6e']//a[text()='Resources']";
    public static $solutionsLink = "//ul[@id='menu-1-4dddc6e']//a[text()='Solutions']";
    public static $logInLink = "//span[text()='Log in']//parent::span//parent::a[@href='https://app.versium.com/']";
    public static $tryItFreeLink = "//div[@data-id='7378e0d']//a[@href='https://app.versium.com/choose-trial']";
    # Links: Product
    public static $reachB2bLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/reach-b2b')][text()='REACH for B2B']";
    public static $reachB2cLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/reach-b2c')][text()='REACH for B2C']";
    public static $reachAPIsLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, 'api-documentation.versium.com/')][text()='REACH APIs']";
    public static $versiumPredictLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/predict')]";
    # Links: Pricing
    public static $pricingB2bLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/b2b-pricing')][text()='B2B Pricing']";
    public static $pricingB2cLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/b2c-pricing')][text()='B2C Pricing']";
    # Links: Resources
    public static $aboutVersiumLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/about-versium')][text()='About Versium']";
    public static $webinarsLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/webinars')][text()='Webinars']";
    public static $caseStudiesLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/case-studies')][text()='Case Studies']";
    public static $techGlossaryLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/tech-glossary')][text()='Tech Glossary']";
    public static $versiumBlogLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/blogs')][text()='Versium Blog']";
    public static $newsRoomLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/newsroom')][text()='Newsroom']";
    # Links: Solutions
    public static $paidAdvertisingLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/solutions/b2b-paid-advertising')][text()='B2B Paid Advertising']";
    public static $acctBasedMarketingLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/solutions/account-based-marketing')][text()='Account Based Marketing']";
    public static $b2bAgenciesLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/solutions/agencies')][text()='B2B Agencies']";
    public static $hubspotLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/hubspot')][text()='Hubspot']";
    public static $partnersLink = "//ul[@id='menu-1-4dddc6e']//a[contains(@href, '/partners')][text()='Partners']";
}