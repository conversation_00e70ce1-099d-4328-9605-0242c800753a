<?php
/**
 * Created by PhpStorm.
 * User: gchandra<PERSON>uli
 * Date: 5/31/19
 * Time: 10:31 AM
 */

namespace Tests\Support\Page;

class Login {
    public static $URL = '/login';

    public static $title = 'REACH - Log In';

    public static $versiumReachImage = "//img[@src='/assets/versium-reach-dark-DnAiDjid.svg']";

    public static $message1 = "You handle the marketing.";
    public static $message2 = "We’ll handle the data.";

    public static $emailField = "//input[@name='email']";
    public static $emailFieldLabel = 'Email Address';

    public static $passwordField = "//input[@name='password']";
    public static $passwordFieldLabel = 'Password';

    public static $logInButton = "//button//span[contains(text(),'Log In')]";
    public static $welcomeBG = "//div[@class='vs-logged-out-page-template__welcome-bg vs-logged-out-page-template__welcome-bg--default col-6 d-none d-lg-flex flex-column px-5']";

    public static $accountSignUpLink = ['Create an Account', '/create-account'];
    public static $forgotPasswordLink = ['Forgot Password', '/password-forgot'];

    private static $elements = null;
    private static $strings = null;
    private static $links = null;

    public static function getStrings() {
        if (self::$strings == null) {
            self::$strings = array(
                self::$message1,
                self::$message2,
                self::$emailFieldLabel,
                self::$passwordFieldLabel,
            );
        }
        return self::$strings;
    }

    public static function getElements() {
        if (self::$elements == null) {
            self::$elements = array(
                self::$versiumReachImage,
                self::$logInButton,
                self::$welcomeBG,
            );
        }
        return self::$elements;
    }

    public static function getLinks() {
        if (self::$links == null) {
            self::$links = array(
                self::$forgotPasswordLink,
                self::$accountSignUpLink
            );
        }
        return self::$links;
    }
}