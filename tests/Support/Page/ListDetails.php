<?php

namespace Tests\Support\Page;

class ListDetails {
    // Card Headers
    public static $fileDetailsHeader = "//h3[text()='File Details']";
    public static $suppressionExclusionHeader = "//h3[text()='Suppression and Exclusion']";
    public static $appendInputHeader = "//h3[text()='Append Input & Output']";
    public static $personaFiltersHeader = "//h3[text()='Persona Filters']";
    public static $ABMFiltersHeader = "//h3[text()='ABM Filters']";
    public static $strictMatchSettingsHeader = "//h3[text()='Strict Match Settings']";
    public static $listGenHeader = "List Generation";

    // FileDetails Strings (ALL jobs)
    public static $fileName = "File Name";
    public static $originalFileName = "Original File Name";
    public static $listType = "List Type";
    public static $listSize = "List Size";
    public static $createdBy = "Created By";
    public static $dateCreated = "Date Created";
    // SuppressionExclusion Strings (ALL jobs)
    public static $activeSuppressionLists = "Active Suppression Lists";
    public static $appliedExclusionLists = "Applied Exclusion Lists";
    // Append Input/Output Strings (Online Audience Append B2B/B2C, B2C Contact Append)
    public static $recordsInFile = "Number of records in file";
    public static $inputCategoriesMapped = "Input Categories Mapped";
    public static $onlineAudienceAppends = "Online Audience Appends";
    public static $matchTypeChosen = "Match Type Chosen";
    // Persona Filter Strings (Persona Audience)
    public static $industry = "Industry";
    public static $employeeCount = "Employee Count";
    public static $revenue = "Revenue";
    public static $titleSeniority = "Title Seniority";
    public static $locationPersona = "Location";
    // ABM Filter Strings (Account-Base Audience)
    public static $peoplePerCompany = "People Per Company";
    // Stric Match Settings Strings (Lookalike Audience)
    public static $matchType = "Match Type";
    // B2C List Generation Strings
    public static $requiredContactData = "Required Contact Data";
    public static $dataAvailable = "Contact Data added if available";
    public static $locationListGen = "Location";
    public static $recordsGenerated = "Records Generated";


    private static $fileDetailsStrings = null;
    private static $suppressionExclusionStrings = null;
    private static $appendInputOutputStrings = null;

    public static function getFileDetailsStrings()
    {
        if (self::$fileDetailsStrings == null) {
            self::$fileDetailsStrings = [
                self::$fileName,
                self::$originalFileName,
                self::$listType,
                self::$listSize,
                self::$createdBy,
                self::$dateCreated
            ];
        }
        return self::$fileDetailsStrings;
    }

    public static function getSuppressionExclusionStrings()
    {
        if (self::$suppressionExclusionStrings == null) {
            self::$suppressionExclusionStrings = [
                self::$activeSuppressionLists,
                self::$appliedExclusionLists
            ];
        }
        return self::$suppressionExclusionStrings;
    }

    public static function getAppendInputOutputStrings()
    {
        if (self::$appendInputOutputStrings == null) {
            self::$appendInputOutputStrings = [
                self::$recordsInFile,
                self::$inputCategoriesMapped,
                self::$onlineAudienceAppends
            ];
        }
        return self::$appendInputOutputStrings;
    }
}