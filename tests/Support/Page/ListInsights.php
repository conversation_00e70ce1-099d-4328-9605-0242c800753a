<?php
/**
 * Created by PhpStorm.
 * User: gchandramouli
 * Date: 6/4/19
 * Time: 2:10 PM
 */

namespace Tests\Support\Page;

class ListInsights {
    //TODO: this class needs a better name
    public static $headerMessageString = 'Use a new list';
    public static $listInput = "//input[@type='file']";

    public static $useExistingListCard = "//h3[text()='Use an existing list']";
    public static $projectSelect = "//select[@name='select a project']";
    public static $listNameSelect = "//select[@name='select a list']";

    public static $uploadCompleteString = "List imported!";
    public static $importListNextStepButton = "//button[contains(.,'Next Step')]";

    public static $useExistingListNextStepButton = "//section[contains(@class, 'vs-import-list')]//span[text()='Next Step']/parent::button";
    public static $mapInputsHeaderFormatString = "Imported List: %s";
    public static $mapFieldsNextStepButton = "//section[contains(@class, 'vs-map-fields container')]//button[contains(@class, 'vs-step-wizard-btn--continue')]";

    # Choose Project Page
    public static $choseProjectHeaderString = "Keep your audiences and lists organized inside projects."; //B2C specific?
    public static $projectNameInput = "//input[@class='vs-creatable-dropdown__input']";
    public static $createButton = "//span[text()='Create']//parent::button";

    public static $importFromHubspotLink = "//div[@id='vs-import-hubspot']//a";
    public static $hubspotListSelect = "//select[@name='contactList']";
    public static $importFromHubspotNextStepBtn = "//span[text()='Next Step']/ancestor::button";

    public static $lookAlikeAttributeProcessorNextStepButton = "//section[contains(@class, 'vs-attribute-processing')]//span[text()='Next Step']";
    public static $abmFilterNextStepButton = "//span[@id='vs-filters-and-preview__next-step']/parent::button";
    public static $waitText = "Versium REACH is finding insights...";
}