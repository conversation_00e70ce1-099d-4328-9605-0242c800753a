actor: ApiTester
modules:
  enabled:
    - Tests\Support\Helper\Api
    - Asserts
    - REST:
        url: https://api.versium.com/v2
        depends: PhpBrowser
        part: Json

env:
  staging:
    modules:
      config:
        REST:
          url: https://api-stg.versium.com/v2
        Tests\Support\Helper\Api:
          environment: 'staging'

  prod:
    modules:
      config:
        REST:
          url: https://api.versium.com/v2
        Tests\Support\Helper\Api:
          environment: 'prod'