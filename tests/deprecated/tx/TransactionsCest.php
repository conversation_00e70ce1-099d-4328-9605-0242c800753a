<?php

use \Page\AccountSettings as AccountSettingsPage;
use \Page\BillingHistory as BillingHistoryPage;
use \Page\Dashboard as DashboardPage;
use \Page\PaymentDetails as PaymentDetailsPage;
use \Page\Visualization as VisualizationPage;

use Codeception\Attribute\Skip;

date_default_timezone_set('America/Los_Angeles');

class TransactionsCest {

    # Run groups in order:
        # trans:trialend
        # trans:billing
        # trans:list

    public function _before(AcceptanceTester $I) {
        $I->login($I->getEmail(), $I->getPassword());
    }

    public function _after(AcceptanceTester $I) {
        $I->logout();
    }

    private static function navigateToPaymentDetails(AcceptanceTester $I) {
        $I->waitForElementAndClick(DashboardPage::$accountSettingsButton);
        $I->waitForElementAndClick(DashboardPage::$accountSettingsLink);
        $I->waitForTextAndClick(AccountSettingsPage::$paymentDetailsLink[0]);
    }

    private static function navigateToBillingHistory(AcceptanceTester $I) {
        $I->waitForElementAndClick(DashboardPage::$accountSettingsButton);
        $I->waitForElementAndClick(DashboardPage::$accountSettingsLink);
        $I->waitForTextAndClick(AccountSettingsPage::$billingHistoryLink[0]);
    }


    ########## Trial End Flow ##########
    /**
     * @group tx
     */
    #[Skip()]
    public function trialEndContactSales(AcceptanceTester $I) {
        $I->waitForElement(DashboardPage::$trialEndBar);
        $I->waitForElementAndClick(DashboardPage::$viewOptionsBtn);
        $I->waitForText(DashboardPage::$trialModalHeaderText);
        $I->waitForElementAndClick(DashboardPage::$contactSalesBtn);
        $I->waitForText(DashboardPage::$contactSalesSuccessText);
        // Click "You can close this window" button
        $I->executeJS('document.getElementsByTagName(\'button\')[2].click()');
    }

    /**
     * @group tx
     */
    #[Skip()]
    public function trialEndAddCC(AcceptanceTester $I) {
        $I->waitForElement(DashboardPage::$trialEndBar);
        $I->waitForElementAndClick(DashboardPage::$viewOptionsBtn);
        $I->waitForElementAndClick(DashboardPage::$enterCardDetailsBtn);
        $I->wait(3);
        // $I->waitForElement(DashboardPage::$enterCardDetailsBtn);

        $I->fillField(PaymentDetailsPage::$emailInput, '<EMAIL>');
        $I->fillField(PaymentDetailsPage::$nameInput, 'Tristan Bull');
        $I->fillField(PaymentDetailsPage::$addrInput, '7530 164th Ave NE');
        $I->fillField(PaymentDetailsPage::$cityInput, 'Redmond');
        $I->selectOption(PaymentDetailsPage::$stateDropdown, 'WA');
        $I->fillField(PaymentDetailsPage::$zipInput, '98122');
        $I->click("//iframe[@title='Secure card payment input frame']");
        $I->sendKeys(****************);
        $I->sendKeys(1123);
        $I->sendKeys(999);
        $I->click(PaymentDetailsPage::$continueBtn);

        $I->waitForElement(PaymentDetailsPage::$cardSavedToast);
        $I->waitForElement(PaymentDetailsPage::$transactionsEnabledToast);
        $I->dontSeeElement(DashboardPage::$trialEndBar);
    }


    ########## Billing Details Flow ##########
    /**
     * @group tx
     */
    #[Skip()]
    public function addCreditCardWithMockAuth(AcceptanceTester $I) {
        self::navigateToPaymentDetails($I);
        $I->waitForElement(PaymentDetailsPage::$emailInput);

        $I->fillField(PaymentDetailsPage::$emailInput, '<EMAIL>');
        $I->fillField(PaymentDetailsPage::$nameInput, 'Tristan Bull');
        $I->fillField(PaymentDetailsPage::$addrInput, '7530 164th Ave NE');
        $I->fillField(PaymentDetailsPage::$cityInput, 'Redmond');
        $I->selectOption(PaymentDetailsPage::$stateDropdown, 'WA');
        $I->fillField(PaymentDetailsPage::$zipInput, '98122');

        $I->scrollSettingsPage(0, 1000);

        $I->click("//iframe[@title='Secure card payment input frame']");
        // $I->sendKeys(****************); // Stripe Mock Auth CC
        $I->sendKeys(****************);
        $I->sendKeys(1123);
        $I->sendKeys(999);
        $I->click(PaymentDetailsPage::$continueBtn);

        // TODO: Figure out iFrame
        // $I->switchToIFrame("//iframe[@name='acsFrame']");
        // $I->waitForElementAndClick("//button[text()='Complete authentication']");

        $I->waitForElement(PaymentDetailsPage::$cardSavedToast);
        $I->waitForText('Card ****4444 - Expires 11/2023');
    }

    /**
     * @group tx
     * @depends addCreditCardWithMockAuth
     */
    #[Skip()]
    public function addCreditCardFromModal(AcceptanceTester $I) {
        self::navigateToPaymentDetails($I);
        $I->waitForElementAndClick(PaymentDetailsPage::$addNewCardBtn);

        $I->waitForElement(PaymentDetailsPage::$emailInput);

        $I->fillField(PaymentDetailsPage::$emailInput, '<EMAIL>');
        $I->fillField(PaymentDetailsPage::$nameInput, 'Tristan Bull');
        $I->fillField(PaymentDetailsPage::$addrInput, '7530 164th Ave NE');
        $I->fillField(PaymentDetailsPage::$cityInput, 'Redmond');
        $I->selectOption(PaymentDetailsPage::$stateDropdown, 'WA');
        $I->fillField(PaymentDetailsPage::$zipInput, '98122');
        $I->click("//iframe[@title='Secure card payment input frame']");
        $I->sendKeys(****************);
        $I->sendKeys(1123);
        $I->sendKeys(999);

        $I->click(PaymentDetailsPage::$continueBtn);

        $I->waitForElement(PaymentDetailsPage::$cardSavedToast);
        $I->waitForText('Card ****4242 - Expires 11/2023');
    }

    /**
     * @group tx
     * @depends addCreditCardFromModal
     */
    #[Skip()]
    public function editBillingEmail(AcceptanceTester $I) {
        self::navigateToPaymentDetails($I);
        $CCLocatorString = 'Card ****4242 - Expires 11/2023';

        $editEmailBtn = sprintf(PaymentDetailsPage::$editEmailBtnFormatString, $CCLocatorString);
        $I->waitForElementAndClick($editEmailBtn);

        $billingEmailInput = sprintf(PaymentDetailsPage::$billingEmailInputFormatString, $CCLocatorString);
        $existingEmail = $I->grabValueFrom($billingEmailInput);
        // Clear the input field
        for ($i = 0; $i < strlen($existingEmail); $i++) {
            $I->pressKey($billingEmailInput, \Facebook\WebDriver\WebDriverKeys::BACKSPACE);
        }
        $I->fillField($billingEmailInput, '<EMAIL>');

        $billingEmailSaveBtn = sprintf(PaymentDetailsPage::$billingEmailSaveBtnFormatString, $CCLocatorString);
        $I->waitForElementAndClick($billingEmailSaveBtn);

        $I->waitForElement(PaymentDetailsPage::$billingEmailUpdatedToast);
        $I->waitForText('Billing email address: <EMAIL>');
    }

    /**
     * @group tx
     * @depends addCreditCardFromModal
     */
    #[Skip()]
    public function makeCCPrimary(AcceptanceTester $I) {
        self::navigateToPaymentDetails($I);
        $CCLocatorString = 'Card ****4242 - Expires 11/2023';

        $makePrimaryBtn = sprintf(PaymentDetailsPage::$makePrimaryBtnFormatString, $CCLocatorString);
        $I->waitForElementAndClick($makePrimaryBtn);

        $I->waitForElement(PaymentDetailsPage::$primaryCardToast);

        $primaryLabel = sprintf(PaymentDetailsPage::$primaryLabelFormatString, $CCLocatorString);
        $I->waitForElement($primaryLabel);
    }

    /**
     * @group tx
     */
    #[Skip()]
    public function deleteCreditCard(AcceptanceTester $I) {
        self::navigateToPaymentDetails($I);
        $CCLocatorString = 'Card ****4242 - Expires 11/2023';

        $I->waitForText($CCLocatorString);

        $deleteCardBtn = sprintf(PaymentDetailsPage::$deleteCardBtnFormatString, $CCLocatorString);
        $I->waitForElementAndClick($deleteCardBtn);
        $I->waitForElementAndClick(PaymentDetailsPage::$deleteConfirmationBtn);

        $I->waitForElement(PaymentDetailsPage::$cardDeletedToast);
        $I->dontSee($CCLocatorString);
    }


    ########## Purchase List Flow ##########
    /**
     * @group tx
     */
    #[Skip()]
    public function purchaseListWithExistingCC(AcceptanceTester $I) {
        $I->waitForElementAndClick(DashboardPage::$onlineAudienceCard[0]);
        $I->listInsightsFlow(
            $I,
            'TransactWithCC',
            'test_data.csv',
            $I::ONLINE_AUDIENCE,
            $I::ENTER_PROJECT_NAME
        );
        $I->waitForText(VisualizationPage::$b2bOnlineAudienceFinished, 1800); // this needs to change

        $I->waitForElementAndClick(VisualizationPage::$buyListBtn);
        // $I->waitForElement(VisualizationPage::$CCSelectDropDown);
        // $I->selectOption(VisualizationPage::$CCSelectDropDown, '*Primary*');
        $I->waitForElementAndClick(VisualizationPage::$agreeAndPurchaseBtn);


        // see purchase success
        $I->waitForText(VisualizationPage::$purchaseSuccessText);
        // click export button
        $I->waitForElementAndClick(VisualizationPage::$exportPurchasedListBtn);

    }

    /**
     * @group tx
     * @depends purchaseListWithExistingCC
     */
    #[Skip()]
    public function purchaseShowsInHistory(AcceptanceTester $I) {
        self::navigateToBillingHistory($I);
        $I->waitForElement(BillingHistoryPage::$billingHistoryTable);

        $tableRows = $I->grabMultiple('tr');
        $firstRowString = $tableRows[1];

        $expected = date("m/d/Y") . ' Purchased Online Audience Append $99.00';
        $I->assertEquals($expected, $firstRowString);
    }

    /**
     * @group tx
     * @depends purchaseListWithExistingCC
     */
    #[Skip()]
    public function downloadReciept(AcceptanceTester $I) {
        self::navigateToBillingHistory($I);

        $I->waitForElementAndClick(BillingHistoryPage::$billingHistoryActionBtn);
        $I->waitForElementAndClick(BillingHistoryPage::$downloadReceiptBtn);
        $I->wait(3);
    }



    // MISC:
      // Insights Page has changed: Match Report | List Insights | List Details
}