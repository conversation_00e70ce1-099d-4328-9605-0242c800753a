<?php

use Tests\Support\AcceptanceTester;

use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\AccountSettings as AccountSettingsPage;
use Tests\Support\Page\Integrations as IntegrationsPage;
use Tests\Support\Page\Zigglio as ZigglioPopup;
use Tests\Support\Page\Visualization as VisualizationPage;

class ZigglioCest {

    private static $listName = 'sample-file.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'Zigglio' . time();
        }
        return self::$projectName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }

    private static function navigateToIntegrationsPage(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$accountSettingsButton);
        $I->waitForElementAndClick(DashboardPage::$accountSettingsLink);
        $I->waitForElementAndClick(AccountSettingsPage::$integrationsLink);
        $I->wait(5); // Integrations page taking a long time to load
    }

    /**
     * @group zig
     */
    #[Skip()]
    public function connectFacebook(AcceptanceTester $I)
    {
        self::navigateToIntegrationsPage($I);

        $I->waitForElementAndClick(IntegrationsPage::$connectToFacebookBtn);
        $I->waitForElementAndClick(IntegrationsPage::$goToZigglioBtn);

        $I->wait(4); // wait for Zigglio popup window
        $I->switchToNextTab();

        $I->see(ZigglioPopup::$zigglioBrandText);
        $I->see(ZigglioPopup::$zigglioFBAdsText);
        $I->waitForElementAndClick(ZigglioPopup::$connectBtn);

        // login to FB
        $I->fillField(ZigglioPopup::$fbUsernameInput, $I->getFacebookUsername());
        $I->fillField(ZigglioPopup::$fbPasswordInput, $I->getFacebookPassword());
        $I->click(ZigglioPopup::$fbLoginBtn);

        // manually handle MFA: Begin
        $I->Pause(); // manually eneter 6-digit MFA code now, Dismiss Pause()

        # click "submit code" btn
        $I->waitForElementAndClick("//button[@id='checkpointSubmitButton']");
        # click "Continue" btn, Rmemeber browser
        $I->waitForElementAndClick("//button[@id='checkpointSubmitButton']");
        $I->waitForElementAndClick(ZigglioPopup::$fbNextBtn);
        $I->waitForElementAndClick(ZigglioPopup::$fbDoneBtn);
        $I->waitForElementAndClick(ZigglioPopup::$fbOkBtn);
        $I->waitForElement(ZigglioPopup::$connectSuccessToast);

        $I->closeTab(); // close popup window
        $I->waitForElement(IntegrationsPage::$connectToFacebookBtn); // assert we are back on Reach
    }

    /**
     * @depends connectFacebook
     * @group zig
     */
    #[Skip()]
    public function connectFBAdAccount(AcceptanceTester $I) {
        self::navigateToIntegrationsPage($I);
        $I->waitForElementAndClick(IntegrationsPage::$connectToFacebookBtn);
        $I->waitForElementAndClick(IntegrationsPage::$goToZigglioBtn);
        // wait for Zigglio popup window
        $I->wait(4);
        $I->switchToNextTab();
        $I->see(ZigglioPopup::$connectAdAccountText);
        // select ad account from dropdown
        $I->selectOption(
            ZigglioPopup::$adAccountSelect,
            ZigglioPopup::$adAccountOption
        );
        // click Add Account Btn
        $I->waitForElementAndClick(ZigglioPopup::$connectAdAccountBtn);
        // wait for account to be added
        $I->wait(5);
        $I->switchToWindow();
        // assert we are back on Reach, account added
        $I->see('Account Name: Datafinder');
    }

    /**
     * @depends connectFBAdAccount
     * @group zig
     */
    #[Skip()]
    public function uploadAudienceToFB(AcceptanceTester $I) {
        $I->click(DashboardPage::$onlineAudienceCard[0]);
        $I->listInsightsFlow(
            $I,
            self::getProjectName(),
            self::$listName,
            $I::ONLINE_AUDIENCE,
            $I::ENTER_PROJECT_NAME
        );
        $I->waitForElement(VisualizationPage::$b2bOnlineAudienceFinished, 90);

        // TODO: Export button ID's are NOT consistent
        // $I->exportList($I, 'facebook');
        $I->waitForElementAndClick("//div[@id='export-button']//button");
        $I->waitForElementAndClick(VisualizationPage::$exportToFBCheckbox);
        $I->waitForElementAndClick(VisualizationPage::$confirmExportBtn);

        // success toast

        // verify FB icon appears
        $I->waitForElement(VisualizationPage::$zigglioUploadingIcon);
        $I->moveMouseOver(VisualizationPage::$zigglioUploadingIcon);
        $I->waitForText(VisualizationPage::$zigglioInQueueText);

        // TODO: click export and see loading spinner
    }

    /**
     * @depends connectFBAdAccount
     * @group zig
     */
    #[Skip()]
    public function removeFBAdAccount(AcceptanceTester $I) {
        self::navigateToIntegrationsPage($I);
        $I->dismissAppVersionToast($I);
        $I->waitForElementAndClick(IntegrationsPage::$zigglioManageBtn);

        // wait for Zigglio popup window
        $I->wait(4);
        $I->switchToNextTab();

        $I->see(ZigglioPopup::$removeAdAccountWarningText);
        $I->waitForElementAndClick(ZigglioPopup::$removeAdAccountBtn);
        $I->waitForElementAndClick(ZigglioPopup::$removeAdAccountBtnFinal);
        $I->waitForElement(ZigglioPopup::$removeAdAccountSuccessToast);
        // close pop window
        $I->closeTab();
        // assert we're back on Reach
        $I->waitForElement(IntegrationsPage::$connectToFacebookBtn);
    }

    /**
     * @depends connectFacebook
     * @group zig
     */
    public function disconnectFacebook(AcceptanceTester $I) {
        self::navigateToIntegrationsPage($I);
        $I->waitForElementAndClick(IntegrationsPage::$connectToFacebookBtn);
        $I->waitForElementAndClick(IntegrationsPage::$goToZigglioBtn);
        // wait for Zigglio popup window
        $I->wait(4);
        $I->switchToNextTab();

        $I->see(ZigglioPopup::$deactivationWarningText);
        $I->waitForElementAndClick(ZigglioPopup::$disconnectBtn);
        $I->waitForElementAndClick(ZigglioPopup::$disconnectBtnFinal);
        $I->wait(3);
        $I->switchToWindow();
        $I->waitForElement(IntegrationsPage::$connectToFacebookBtn);
    }

    /**
     * @group zig
     */
    public function connectGoogle(AcceptanceTester $I) {
        // TODO: There is a FB popup window that lingers, and we switch to that window
        // erroneouslly when attempting to switch to the Google window
        self::navigateToIntegrationsPage($I);

        $I->scrollTo("//button[contains(.,'Google')]");
        $I->waitForElementAndClick(IntegrationsPage::$connectToGoogleBtn);
        $I->waitForElementAndClick(IntegrationsPage::$goToZigglioBtn);
        // wait for Zigglio popup window
        $I->wait(4);
        $I->switchToNextTab();
        // Make assertations about Zigglio popup
        $I->see(ZigglioPopup::$zigglioBrandText);
        $I->see(ZigglioPopup::$zigglioGoogleAdsText);
        // click Activate
        $I->waitForElementAndClick(ZigglioPopup::$connectBtn);

        // login to Google
        $I->fillField(ZigglioPopup::$GoogleUsernameInput, $I->getVersiumGoogleUsername());
        $I->waitForElementAndClick(ZigglioPopup::$GoogleNxtBtn);
        $I->wait(2);
        $I->fillField(ZigglioPopup::$GooglePasswordInput, $I->getVersiumGooglePassword());
        $I->waitForElementAndClick(ZigglioPopup::$GoogleNxtBtn);

        // ** ENTER GOOGLE MFA ** //
        $I->Pause(); //TODO: remove pause
        // ** HANDLE THIS APP ISNT VERIFIED ** //

        // handle MFA
        $I->waitForElementAndClick(ZigglioPopup::$GoogleAllowAccessBtn, 120);
        $I->waitForElement(ZigglioPopup::$googleConnectSuccessToast);

        // close popup window
        $I->closeTab();
        // assert we are on Reach
        $I->waitForElement(IntegrationsPage::$connectToGoogleBtn);
    }

    /**
     * @depends connectGoogle
     * @group zig
     */
    public function connectGoogleAdAccount(AcceptanceTester $I) {
        self::navigateToIntegrationsPage($I);

        $I->scrollTo(IntegrationsPage::$connectToFacebookBtn); //Footer is truely evil
        $I->waitForElementAndClick(IntegrationsPage::$connectToGoogleBtn);
        $I->waitForElementAndClick(IntegrationsPage::$goToZigglioBtn);

        // wait for Zigglio popup window
        $I->wait(4);
        $I->switchToNextTab();
        $I->see(ZigglioPopup::$googleSelectCustomerText);
        $I->see(ZigglioPopup::$googleSelectAcctText);
        // select customer
        $I->selectOption(
            ZigglioPopup::$googleCustomerSelect,
            ZigglioPopup::$googleCustomerOption
        );
        $I->wait(8);
        // select account
        $I->selectOption(
            ZigglioPopup::$googleClientAcctSelect,
            ZigglioPopup::$googleCustomerOption
        );
        // click Add Account Btn
        $I->waitForElementAndClick(ZigglioPopup::$googleSaveAdAcctBtn);
        // wait for account to be added
        $I->wait(5);
        $I->switchToWindow();
        // assert we are back on Reach, account added
        $I->see('Account Name: Versium REACH');
    }

    /**
     * @depends connectGoogleAdAccount
     * @group zig
     */
    public function uploadAudienceToGoogle(AcceptanceTester $I) {
        $I->click(DashboardPage::$onlineAudienceCard[0]);
        $I->listInsightsFlow(
            $I,
            self::getProjectName(),
            self::$listName,
            $I::ONLINE_AUDIENCE,
            $I::ENTER_PROJECT_NAME
        );
        $I->waitForText(VisualizationPage::$b2bOnlineAudienceFinished, 90);

        // TODO: Export button ID's are NOT consistent
        // $I->exportList($I, 'google');

        $I->waitForElementAndClick("//div[@id='export-button']//button");
        $I->waitForElementAndClick(VisualizationPage::$exportToGoogleCheckbox);
        $I->waitForElementAndClick(VisualizationPage::$confirmExportBtn);

        // TODO: Verify Success Toast

        $I->waitForElement(VisualizationPage::$zigglioUploadingIcon);
        $I->moveMouseOver(VisualizationPage::$zigglioUploadingIcon);
        $I->waitForText(VisualizationPage::$zigglioInQueueText);
    }

    /**
     * @depends connectGoogleAdAccount
     * @group zig
     */
    public function removeGoogleAdAccount(AcceptanceTester $I) {
        self::navigateToIntegrationsPage($I);

        $I->dismissAppVersionToast($I); // App version toast intercepts click
        $I->scrollTo(IntegrationsPage::$connectToFacebookBtn); //Footer is truely evil
        $I->waitForElementAndClick(IntegrationsPage::$zigglioManageBtn);

        $I->wait(4);
        $I->switchToNextTab();

        $I->see(ZigglioPopup::$removeGoogleAdAcctWarningText);
        $I->waitForElementAndClick(ZigglioPopup::$googleRemoveAdAcctBtn);
        $I->waitForElementAndClick(ZigglioPopup::$googleRemoveAdAcctBtnFinal);
        $I->waitForElement(ZigglioPopup::$googleRemoveAdAcctSuccessToast);
        $I->switchToWindow();

        $I->waitForElement(IntegrationsPage::$connectToGoogleBtn);
    }

    /**
     * @depends connectGoogle
     * @group zig
     */
    public function disconnectGoogle(AcceptanceTester $I) {
        self::navigateToIntegrationsPage($I);

        $I->scrollTo(IntegrationsPage::$connectToFacebookBtn); //Footer is truely evil
        $I->waitForElementAndClick(IntegrationsPage::$connectToGoogleBtn);
        $I->waitForElementAndClick(IntegrationsPage::$goToZigglioBtn);

        $I->wait(4);
        $I->switchToNextTab();

        $I->see(ZigglioPopup::$deactivationWarningText);
        $I->waitForElementAndClick(ZigglioPopup::$disconnectBtn);
        $I->waitForElementAndClick(ZigglioPopup::$disconnectBtnFinal);
        $I->wait(3);
        $I->switchToWindow();

        $I->waitForElement(IntegrationsPage::$connectToGoogleBtn);
    }
}