# Codeception Test Suite Configuration
#
# Suite for Versium.com tests

actor: VersiumDotComTester
extensions:
  enabled:
    - Codeception\Extension\Recorder
    - Codeception\Extension\RunFailed

env:
  staging:
    modules:
      enabled:
      - WebDriver
      - Tests\Support\Helper\VersiumDotCom
      - Asserts
      - Filesystem
      config:
        WebDriver:
          url: 'https://versium:wordpress!@staging.versium.com/'
          browser: 'chrome'
          window_size: true # fullscreen
        Tests\Support\Helper\VersiumDotCom:
          environment: 'staging'

  prod:
    modules:
      enabled:
      - WebDriver
      - Tests\Support\Helper\VersiumDotCom
      - Asserts
      - Filesystem
      config:
        WebDriver:
          url: 'https://versium.com/'
          browser: 'chrome'
          window_size: true # fullscreen
        Tests\Support\Helper\VersiumDotCom:
          environment: 'prod'
