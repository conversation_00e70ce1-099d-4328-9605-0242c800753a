# Codeception Test Suite Configuration
#
# Suite for acceptance tests.
# Perform tests in browser using the WebDriver or PhpBrowser.
# If you need both WebDriver and PHPBrowser tests - create a separate suite.

actor: AcceptanceTester
extensions:
  enabled:
    - Codeception\Extension\Recorder
modules:
  enabled:
    - Asserts
    - Filesystem
    - WebDriver
    - \Tests\Support\Helper\Acceptance
    # - PhpBrowser
  config:
    WebDriver:
      url: 'https://app.versium.com/'
      browser: 'chrome'
      window_size: maximize
    \Tests\Support\Helper\Acceptance:
      email: '%VERSIUM_APP_TEST_EMAIL%'
      password: '%VERSIUM_APP_TEST_PWD%'
    PhpBrowser:
      url: 'https://app.versium.com/'

env:
  dev:
    modules:
      config:
        WebDriver:
          url: 'http://localhost:3000/'

  staging:
    modules:
      config:
        WebDriver:
          url: 'https://staging-app.versium.com/'

  prod:
    modules:
      config:
        WebDriver:
          url: 'https://app.versium.com/'

  customer-report-staging:
    modules:
      config:
        \Tests\Support\Helper\Acceptance:
          email: '<EMAIL>'
          password: '%CUSTOMER_REPORT_STG_PWD%'
        WebDriver:
          url: 'https://staging-app.versium.com/'

  customer-report-prod:
    modules:
      config:
        \Tests\Support\Helper\Acceptance:
          email: '<EMAIL>'
          password: '%CUSTOMER_REPORT_PROD_PWD%'
        WebDriver:
          url: 'https://app.versium.com/'

  compliance-optout-prod:
    modules:
      config:
        \Tests\Support\Helper\Acceptance:
          email: '<EMAIL>'
          password: '%COMPLIANCE_OPT_OUT_PROD_PWD%'
        WebDriver:
          url: 'https://app.versium.com/'
