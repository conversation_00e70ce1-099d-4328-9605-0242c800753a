<?php

namespace Tests\Acceptance\customer;

use Tests\Support\AcceptanceTester;
use Tests\Support\Classes\ListGenGeoFilters;
use Tests\Support\Classes\ListGenContact;
use Tests\Support\Enums\ContactType;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\ProjectsList as ProjectsPage;
use Tests\Support\Page\B2CAttributeSelection as AttributeSelectPage;
use Tests\Support\Page\SideNav;
use Tests\Support\Page\Persona as PersonaPage;
use Codeception\Attribute\Group;
use Tests\Support\Classes\ListGenCustomLimits;

date_default_timezone_set('America/Los_Angeles');

class CustomerCest
{
    // private static $kdInteractiveFileName = 'kd_interactive.csv';
    private static $kdInteractiveFileName = 'kd_interactive_100k.csv';
    private static $sMFinanaceFileName = 's_m_finance.csv';
    private static $leverageLabs1FileName = 'leverage_labs_1.csv';
    private static $leverageLabs2FileName = 'leverage_labs_2.csv';
    private static $inboundInsightFileName = 'inbound_insight.csv';

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->wait(1);
        $I->logout();
    }

    private function getProjectName()
    {
        return date('Y-m-d');
    }


    # create project to hold lists
    #[Group('customer')]
    public function create_project(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(SideNav::$projectsLink);
        $I->wait(1);
        $I->waitForElementAndClick(ProjectsPage::$createProjectButton);
        $I->waitForElement(ProjectsPage::$newProjectInput);
        $I->fillField(ProjectsPage::$newProjectInput, self::getProjectName());
        $I->waitForElementAndClick(ProjectsPage::$createButton);
    }


    # contact append plus: address & phone mobile
    #[Group('customer')]
    public function kd_interactive_contact_append(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashBoardPage::$contactAppendCard);
        $I->doB2cContactAppend(
            ImportListType::NewList,
            self::$kdInteractiveFileName,
            self::getProjectName(),
            [AttributeSelectPage::$addressCheckBoxLabel, AttributeSelectPage::$phoneCheckBoxLabel],
            ProjectType::ExistingProject,
            AttributeSelectPage::$phoneMobileLabel
        );
    }


    # dempgraphic append: all append types
    #[Group('customer')]
    public function kd_interactive_demographic(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$demographicAppendCard);
        $I->doB2cDemographicAppend(
            ImportListType::ExistingList,
            self::$kdInteractiveFileName,
            self::getProjectName(),
            [
                AttributeSelectPage::$basicDemographicCheckboxLabel,
                AttributeSelectPage::$finAutoDemographicCheckboxLabel,
                AttributeSelectPage::$lifestyleDemographicCheckboxLabel,
                AttributeSelectPage::$policatlDemographicCheckboxLabel
            ],
            ProjectType::PreSelectedProject,
        );
    }


    # b2c online audience append
    #[Group('customer')]
    public function kd_interactive_oa(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$b2cOnlineAudienceCard);
        $I->doListInsightsFlow(
            ImportListType::ExistingList,
            self::$kdInteractiveFileName,
            self::getProjectName(),
            ProjectType::PreSelectedProject,
        );
    }


    # contact append: email & phone best
    #[Group('customer')]
    public function s_m_finance_phone_best(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashBoardPage::$contactAppendCard);
        $I->doB2cContactAppend(
            ImportListType::NewList,
            self::$sMFinanaceFileName,
            self::getProjectName(),
            [AttributeSelectPage::$emailCheckBoxLabel, AttributeSelectPage::$phoneCheckBoxLabel],
            ProjectType::ExistingProject,
            AttributeSelectPage::$phoneBestLabel
        );
    }


    # contact append: phone multi
    #[Group('customer')]
    public function s_m_finance_phone_multi(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashBoardPage::$contactAppendCard);
        $I->doB2cContactAppend(
            ImportListType::ExistingList,
            self::$sMFinanaceFileName,
            self::getProjectName(),
            [AttributeSelectPage::$phoneCheckBoxLabel],
            ProjectType::PreSelectedProject,
            AttributeSelectPage::$phoneMultiLabel
        );
    }


    # c2b append
    #[Group('customer')]
    public function inbound_insight(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$c2bAppendCard);
        $I->doC2bAppend(
            ImportListType::NewList,
            self::$inboundInsightFileName,
            self::getProjectName(),
            ProjectType::ExistingProject,
        );
    }


    # firmographic append
    #[Group('customer')]
    public function leverage_labs_firmographic(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$firmographicCard);
        $I->doListInsightsFlow(
            ImportListType::NewList,
            self::$leverageLabs1FileName,
            self::getProjectName(),
            ProjectType::ExistingProject,
        );
    }


    # ip2domain append
    #[Group('customer')]
    public function leverage_labs_ip2domain(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$ip2DomainCard);
        $I->doListInsightsFlow(
            ImportListType::NewList,
            self::$leverageLabs2FileName,
            self::getProjectName(),
            ProjectType::ExistingProject,
        );
    }


    //****************************** LIST GEN ****************************************/

    #[Group('customer')]
    public function list_gen_b2b_1(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$personaListCard);
        $I->doPersonaListFlow(
            'ListGenB2b_1',
            self::getProjectName(),
            ProjectType::ExistingProject,
            ['Group - Business Services (Advertising)'],
            [],
            'omnichannel',
            ['Georgia']
        );
    }


    #[Group('customer')]
    public function list_gen_b2b_2(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$personaListCard);
        $I->doPersonaListFlow(
            'ListGenB2b_2',
            self::getProjectName(),
            ProjectType::ExistingProject,
            ['Group - Business Services (Software, Programming, IT)'],
            [],
            'omnichannel',
            ['Connecticut']
        );
    }


    #[Group('customer')]
    public function list_gen_b2c_1(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            'B2C_ListGen_1',
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone, null, 'When Available'),
                new ListGenContact(ContactType::Email),
            ],
            new ListGenCustomLimits(null, null, true),
            new ListGenGeoFilters(null, null, null, ['60629']),
            null,
            null
        );
    }


    #[Group('customer')]
    public function list_gen_b2c_2(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            'B2C_ListGen_2',
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone, null, 'When Available'),
                new ListGenContact(ContactType::Email),
            ],
            new ListGenCustomLimits(null, null, true),
            new ListGenGeoFilters(null, null, null, ['80219']),
            null,
            null
        );
    }
}