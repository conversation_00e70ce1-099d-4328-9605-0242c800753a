<?php

namespace Tests\Acceptance\customer;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Visualization as VisualizationPage;

class CustomerCollectCest
{
    // TODO: Clean the percent values off of the values that have them

    // TODO: These now exist in both this file and in CustomerCest...
    private static $kdInteractiveFileName = 'kd_interactive_100k';
    private static $sMFinanaceFileName = 's_m_finance';
    private static $leverageLabs1FileName = 'leverage_labs_1'; //firmographic TODO: Add 'firmographic' to file name
    private static $leverageLabs2FileName = 'leverage_labs_2';
    private static $inboundInsightFileName = 'inbound_insight';
    // TODO: have a seperate report for prod / staging
    private static $reportPath = '/Users/<USER>/Development/reach-testing/tests/Acceptance/customer/report.csv';


    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }

    private function getProjectName(): string
    {
        //update as needed
        return '2025-08-01';
    }

    private function scrubSpecialChars($str): string
    {
        return preg_replace('/[^A-Za-z0-9\-\.]/', '', $str);
    }

    private function writeToReport($rows)
    {
        $r = fopen(self::$reportPath, 'a');
        foreach ($rows as $row) {
            fputcsv($r, $row);
        }
        fclose($r);
    }

    public function kd_interactive_contact_append(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$kdInteractiveFileName . '-contact-append' . '.csv',
            true
        );
        $I->wait(1);

        // Get postal adress MR & CPA
        $addrMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Postal Address'));
        $addrCPA = $I->grabTextFrom(sprintf(VisualizationPage::$contactPointsFrmtStr, 'Postal Address'));
        // Get mobile phone MR & CPA
        $phoneMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Phone Number'));
        $phoneCPA = $I->grabTextFrom(sprintf(VisualizationPage::$contactPointsFrmtStr, 'Phone Number'));

        self::writeToReport([
            ['KD Interactive - B2C Contact Append'],
            ['Phone Mobile MR', self::scrubSpecialChars($phoneMR)],
            ['Phone Mobile CPA', $phoneCPA],
            ['Address MR', self::scrubSpecialChars($addrMR)],
            ['Address CPA', $addrCPA],
        ]);
    }

    public function kd_interactive_demographic(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$kdInteractiveFileName . '-demographic-append' . '.csv',
            true
        );
        $I->wait(1);

        // Get demographic match rates
        $demoMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Basic Demographic'));
        $houseFinAutoMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Household, Financial and Auto'));
        $lifestyleInterestMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Lifestyle and Interests'));
        $politicalMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Political and Donor'));

        self::writeToReport([
            ['KD Interactive - B2C Demographic Append'],
            ['Demographic MR', self::scrubSpecialChars($demoMR)],
            ['Household Financial Auto MR', self::scrubSpecialChars($houseFinAutoMR)],
            ['Lifestyle Interest MR', self::scrubSpecialChars($lifestyleInterestMR)],
            ['Political MR', self::scrubSpecialChars($politicalMR)]
        ]);
    }

    public function kd_interactive_oa(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$kdInteractiveFileName . '-b2c-audience-extension' . '.csv',
            true
        );
        $I->wait(1);

        // Get KPI's
        $emailMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Email Address'));
        $phoneMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Phone Number'));
        $locationMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Location Data'));

        self::writeToReport([
            ['KD Interactive - B2C Online Audience'],
            ['Email 1 MR', self::scrubSpecialChars($emailMR)],
            ['Phone 1 MR', self::scrubSpecialChars($phoneMR)],
            ['Location MR', self::scrubSpecialChars($locationMR)]
        ]);
    }

    public function s_m_finance_phone_best(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$sMFinanaceFileName . '-contact-append' . '.csv',
            true
        );
        $I->wait(1);

        // Get KPIs
        $emailMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Email Address'));
        $emailCPA = $I->grabTextFrom(sprintf(VisualizationPage::$contactPointsFrmtStr, 'Email Address'));
        $phoneBestMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Phone Number'));
        $phoneBestCPA = $I->grabTextFrom(sprintf(VisualizationPage::$contactPointsFrmtStr, 'Phone Number'));

        self::writeToReport([
            ['S&M Finance File - B2C Phone/Email Append'],
            ['Phone Best MR', self::scrubSpecialChars($phoneBestMR)],
            ['Phone Best CPA', $phoneBestCPA],
            ['Email MR', self::scrubSpecialChars($emailMR)],
            ['Email CPA', $emailCPA],
        ]);
    }

    public function s_m_finance_phone_multi(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$sMFinanaceFileName . '-contact-append-1' . '.csv',
            true
        );
        $I->wait(1);

        // Get KPIs
        $phoneMultiMR = $I->grabTextFrom(sprintf(VisualizationPage::$matchRateFrmtStr, 'Phone Number'));
        $phoneMultiCPA = $I->grabTextFrom(sprintf(VisualizationPage::$contactPointsFrmtStr, 'Phone Number'));

        self::writeToReport([
            ['Phone Multi MR', self::scrubSpecialChars($phoneMultiMR)],
            ['Phone Multi CPA', $phoneMultiCPA]
        ]);
    }

    public function leverage_labs_firmographic(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$leverageLabs1FileName . '-firmographic-append' . '.csv'
        );
        $I->wait(1);

        // Get KPIs
        $firmoMatches = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Matches'));
        $attrsAppended = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Attributes Appended'));

        self::writeToReport([
            ['Leverage Labs File 1 - Firmographic'],
            ['Matches', $firmoMatches],
            ['Attrs Appended', $attrsAppended]
        ]);
    }

    public function leverage_labs_ip2domain(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$leverageLabs2FileName . '-ip-domain-append' . '.csv'
        );
        $I->wait(1);

        // Get KPIs
        $matches = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Matches'));
        $domainsAppended = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Domains Appended'));

        self::writeToReport([
            ['Leverage Labs File 2 - IP2Domain'],
            ['Matches', $matches],
            ['Domains Appended', $domainsAppended]
        ]);
    }

    public function inbound_insight(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$inboundInsightFileName . '-c2b-append' . '.csv',
            true
        );
        $I->wait(1);

        // Get KPIs
        $c2bMatches = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Matches'));
        $uniqueCompanies = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Unique Companies'));

        self::writeToReport([
            ['Inbound Insight File - C2B Append'],
            ['C2B Append Matches', $c2bMatches],
            ['Unique Companies', $uniqueCompanies]
        ]);
    }

    public function list_gen_b2b_1(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            'ListGenB2b_1.csv'
        );
        $I->wait(1);

        // Get KPIs
        $emails = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Email Addresses'));
        $oaRecords = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Online Audience Records'));
        $uniqueCompanies = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Unique Companies'));
        $totalContactPoints = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Total Contact Points'));

        // Write to file
        self::writeToReport([
            ['B2B Persona, Filter 1'],
            ['Email Addresses', $emails],
            ['Online Audience Recs', $oaRecords],
            ['Unique Companies', $uniqueCompanies],
            ['Total CPA', $totalContactPoints]
        ]);
    }

    public function list_gen_b2b_2(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            'ListGenB2b_2.csv'
        );
        $I->wait(1);

        // Get KPIs
        $emails = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Email Addresses'));
        $oaRecords = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Online Audience Records'));
        $uniqueCompanies = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Unique Companies'));
        $totalContactPoints = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Total Contact Points'));

        self::writeToReport([
            ['B2B Persona, Filter 2'],
            ['Email Addresses', $emails],
            ['Online Audience Recs', $oaRecords],
            ['Unique Companies', $uniqueCompanies],
            ['Total CPA', $totalContactPoints]
        ]);
    }

    public function list_gen_b2c_1(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            'B2C_ListGen_1.csv'
        );
        $I->wait(1);

        // Get KPIs
        $contacts = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Contacts Generated'));

        self::writeToReport([
            ['B2C ListGen, Filter 1'],
            ['Contacts Generated', $contacts],
        ]);
    }

    public function list_gen_b2c_2(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            'B2C_ListGen_2.csv'
        );
        $I->wait(1);

        // Get KPIs
        $contacts = $I->grabTextFrom(sprintf(VisualizationPage::$countBoxFrmtStr, 'Contacts Generated'));

        self::writeToReport([
            ['B2C ListGen, Filter 2'],
            ['Contacts Generated', $contacts],
        ]);
    }
}
