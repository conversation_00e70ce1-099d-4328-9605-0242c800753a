<?php

namespace Tests\Acceptance\b2c;

use Tests\Support\AcceptanceTester;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Tests\Support\Enums\JobType;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Visualization as VisualizationPage;
use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;


class OnlineAudienceCest
{
    private static $listName = 'consumer.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'OnlineAudience' . time();
        }
        return self::$projectName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    public function createOnlineAudience(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$b2cOnlineAudienceCard);
        $I->waitForText('Create a B2C Online Audience');

        $I->doListInsightsFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject,
        );
        $I->waitForElement(VisualizationPage::$b2cOnlineAudienceAppendFinishedElem, 1800);
    }


    #[Depends('createOnlineAudience')]
    public function createOnlineAudienceFromExistingList(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$b2cOnlineAudienceCard);
        $I->waitForText('Create a B2C Online Audience');

        $I->doListInsightsFlow(
            ImportListType::ExistingList,
            self::$listName,
            self::getProjectName(),
            ProjectType::PreSelectedProject,
        );
        $I->waitForElement(VisualizationPage::$b2cOnlineAudienceAppendFinishedElem, 1800);
    }


    #[Group('hubspot')]
    #[Depends('createOnlineAudience')]
    public function createOnlineAudienceFromHubspot(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$b2cOnlineAudienceCard);
        $I->waitForText('Create a B2C Online Audience');

        $I->doListInsightsFlow(
            ImportListType::HubspotList,
            $I::HUBSPOT_LIST_NAME,
            self::getProjectName(),
            ProjectType::ExistingProject,
        );
        $I->waitForElement(VisualizationPage::$b2cOnlineAudienceAppendFinishedElem, 1800);
    }


    #[Depends('createOnlineAudience')]
    public function createOnlineAudienceFromListActions(AcceptanceTester $I)
    {
        $projectName = self::getProjectName();

        $I->doB2CListAction(
            $projectName,
            self::$listName,
            JobType::B2cOnlineAudience,
        );
        $I->doListInsightsFlow(
            ImportListType::ExistingList,
            self::$listName,
            $projectName,
            ProjectType::PreSelectedProject,
        );
        $I->waitForElement(VisualizationPage::$b2cOnlineAudienceAppendFinishedElem, 1800);
    }


    #[Group('sanity')]
    #[Depends('createOnlineAudience')]
    public function exportList(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            basename(self::$listName, '.csv') . '-b2c-audience-extension.csv',
        );

        $I->waitForElementAndClick(VisualizationPage::$exportB2cOABtn);
        $I->waitForElementAndClick(VisualizationPage::$confirmExportBtn);
        $I->waitForElementAndClick(VisualizationPage::$acceptTOSBtn);
        $I->wait(2);
    }
}