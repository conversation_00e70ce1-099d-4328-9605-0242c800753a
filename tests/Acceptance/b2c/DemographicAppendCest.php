<?php

namespace Tests\Acceptance\b2c;

use Tests\Support\AcceptanceTester;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Tests\Support\Page\Visualization as VisualizationPage;
use Tests\Support\Page\B2CAttributeSelection as AttributeSelectPage;
use Tests\Support\Page\Dashboard as DashboardPage;
use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;
use Tests\Support\Enums\JobType;

class DemographicAppendCest
{
    private static $listName = 'consumer.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'DemographicAppend' . time();
        }
        return self::$projectName;
    }


    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->wait(1);
        $I->logout();
    }


    #[Group('sanity')]
    public function createDemographicAppend(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$demographicAppendCard);
        $I->doB2cDemographicAppend(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            [AttributeSelectPage::$basicDemographicCheckboxLabel],
            ProjectType::NewProject,
        );
        $I->waitForElement(VisualizationPage::$b2cDemoAppendFinishedElem, 1800);
    }


    #[Depends('createDemographicAppend')]
    public function createDemographicAppendFromExistingList(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$demographicAppendCard);
        $I->doB2cDemographicAppend(
            ImportListType::ExistingList,
            self::$listName,
            self::getProjectName(),
            [AttributeSelectPage::$basicDemographicCheckboxLabel],
            ProjectType::PreSelectedProject,
        );
        $I->waitForElement(VisualizationPage::$b2cDemoAppendFinishedElem, 1800);
    }


    #[Group('hubspot')]
    #[Depends('createDemographicAppend')]
    public function createDemographicAppendFromHubspot(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$demographicAppendCard);
        $I->doB2cDemographicAppend(
            ImportListType::HubspotList,
            $I::HUBSPOT_LIST_NAME,
            self::getProjectName(),
            [AttributeSelectPage::$basicDemographicCheckboxLabel],
            ProjectType::ExistingProject,
        );
        $I->waitForElement(VisualizationPage::$b2cDemoAppendFinishedElem, 1800);
    }


    #[Depends('createDemographicAppend')]
    public function createDemographicAppendFromListActions(AcceptanceTester $I)
    {
        $projectName = self::getProjectName();

        $I->doB2CListAction(
            $projectName,
            self::$listName,
            JobType::DemographicAppend
        );
        $I->doB2cDemographicAppend(
            ImportListType::ExistingList,
            self::$listName,
            $projectName,
            [AttributeSelectPage::$basicDemographicCheckboxLabel],
            ProjectType::PreSelectedProject,
        );
        $I->waitForElement(VisualizationPage::$b2cDemoAppendFinishedElem, 1800);
    }


    #[Group('sanity')]
    #[Depends('createDemographicAppend')]
    public function exportList(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            basename(self::$listName, '.csv') . '-demographic-append.csv',
        );

        $I->waitForElementAndClick(VisualizationPage::$secondaryResultsBtn);
        $I->waitForElementAndClick(VisualizationPage::$acceptTOSBtn);
        $I->wait(2);
    }


    #[Depends('exportList')]
    public function exportedListHasData(AcceptanceTester $I)
    {
        $listName = basename(self::$listName, ".csv") . '-demographic-append.csv';

        $I->openFile($I->getDownloadDir() . $listName);
        $I->seeInThisFile('Sender_Email,Phone,"Address 1","Address 2",City,State,Zip,FirstName,LastName');
    }
}
