<?php

namespace Tests\Acceptance\b2c;

use Tests\Support\AcceptanceTester;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Tests\Support\Enums\ListAction;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Project as ProjectPage;
use Tests\Support\Page\ListActions as ListActionsMenu;
use Tests\Support\Page\Visualization as VisualizationPage;
use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;


class ListInsightsCest
{
    private static $listName = 'consumer.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'ListInsights' . time();
        }
        return self::$projectName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    public function createListInsightsFromMainDashboard(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$b2cListInsightsCard);
        $I->doListInsightsFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject,
        );
        $I->waitForElement(VisualizationPage::$firstResultsCard, 500);
    }


    #[Group('sanity')]
    #[Group('hubspot')]
    #[Depends('createListInsightsFromMainDashboard')]
    public function createListInsightsFromMainDashboardImportHubSpot(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$b2cListInsightsCard);
        $I->doListInsightsFlow(
            ImportListType::HubspotList,
            $I::HUBSPOT_LIST_NAME,
            self::getProjectName(),
            ProjectType::ExistingProject,
        );
        $I->wait(2);
    }


    #[Depends('createListInsightsFromMainDashboard')]
    public function createListInsightsFromProjectDashboard(AcceptanceTester $I)
    {
        $projectName = self::getProjectName();
        $I->openProject($projectName);

        // Uncomment this line if user has both b2c and b2b tools
        // $I->waitForElementAndClick("//a[text()='Consumer Data Tools']");
        $I->waitForElementAndClick("//a[contains(.,'List Insights')]");

        $I->doListInsightsFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::PreSelectedProject,
        );
        $I->wait(2);
    }


    #[Depends('createListInsightsFromMainDashboard')]
    public function verifyListActionMenu(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$listName
        );

        $listNameElem = sprintf(ProjectPage::$listLocatorFormatString, self::$listName);

        $I->assertEquals(
            self::$listName,
            $I->grabTextFrom($listNameElem),
            "Displayed list name is not same as uploaded list name"
        );

        $listActionButton = $listNameElem . '/parent::div/parent::div/parent::a/following-sibling::button';

        // open actions menu
        $I->click($listActionButton);
        $I->wait(2);
        $I->canSeeElements(ListActionsMenu::getElements());
        // close actions menu
        $I->click($listActionButton);
        $I->wait(2);
        // open actions menu again
        $I->click($listActionButton);
        $I->wait(2);
    }


    #[Group('sanity')]
    #[Depends('createListInsightsFromMainDashboard')]
    public function verifyListInsights(AcceptanceTester $I) {
        $I->openListFromProject(
            self::getProjectName(),
            self::$listName,
        );
        $I->waitForElement(VisualizationPage::$firstResultsCard);

        $visualizationHeaderText = $I->grabTextFrom(VisualizationPage::$firstResultsCard);
        $expectedText = 'The people in this list are predominantly Female, located in 29 states with most in the Northern region. The highest percentage age range is 45 to 54.';
        $I->assertEquals($expectedText, $visualizationHeaderText);

        $I->validateVisualizations($I, $I::B2C);
    }


    #[Group('sanity')]
    #[Depends('createListInsightsFromMainDashboard')]
    public function exportList(AcceptanceTester $I)
    {
        $I->doB2CListAction(
            self::getProjectName(),
            self::$listName,
            ListAction::Export,
        );
        $I->wait(3);
    }


    #[Depends('createListInsightsFromMainDashboard')]
    public function shareInsights(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$listName
        );
        // wait for insights btn
        $I->waitForElementClickable(VisualizationPage::$shareInsightsBtn);
        $I->moveMouseOver(VisualizationPage::$shareInsightsBtn);
        $I->click(VisualizationPage::$shareInsightsBtn);
        // share insights pop-up
        $I->waitForText(VisualizationPage::$shareInsightsText);
        $I->fillField(VisualizationPage::$sendInviteInput, '<EMAIL>');
        $I->waitForElementAndClick(VisualizationPage::$sendInviteBtn);
        $I->waitForText(VisualizationPage::$successToastText);
    }


    #[Group('sanity')]
    #[Depends('createListInsightsFromMainDashboard')]
    public function downloadVisualizations(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$listName,
        );

        $I->wait(3);

        $I->moveMouseOver(VisualizationPage::$downloadAsPDFBtn);
        $I->wait(1);
        $I->waitForText(VisualizationPage::$PDFDownloadReadyText);

        $I->moveMouseOver(VisualizationPage::$downloadAsPNGBtn);
        $I->wait(1);
        $I->waitForText(VisualizationPage::$PNGDownloadReadyText);

        $I->waitForElementAndClick(VisualizationPage::$downloadAsPDFBtn);
        $I->wait(1);
        $I->waitForElementAndClick(VisualizationPage::$downloadAsPNGBtn);
        $I->wait(1);
    }
}