<?php

namespace tests\Acceptance\DataTools\DataPrep;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\DataPrep;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Tests\Support\Enums\MatchType;
use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;
use Codeception\Attribute\Skip;

class DataPrepCest
{
    private static $listName = 'sample-file-real-dirty.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'DataPrep' . time();
        }
        return self::$projectName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    public function doDataPrepFlow(AcceptanceTester $I)
    {
        $I->doDataPrepFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject,
        );
        $I->waitForElement(DataPrep::$resultsElem, 60);
    }

    #[Group('sanity')]
    #[Depends('doDataPrepFlow')]
    public function exportDataPrepList(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            basename(self::$listName, '.csv') . '-data-prep-output.csv',
        );

        $I->waitForElementAndClick(DataPrep::$exportBtn);
        $I->waitForText(DataPrep::$exportModalText, 10);
        $I->click(DataPrep::$exportModalCloseBtn);
        $I->wait(1);
    }

    public function addRemoveSuggestedActions(AcceptanceTester $I)
    {
        $I->_goToDataPrepWorkArea(ImportListType::NewList, self::$listName);
        #add Suggested Activity
        $I->click(sprintf(DataPrep::$suggestedActionsElem, 'Capitalize Words'));
        $I->wait(1);
        #remove Suggested Activity
        $I->click(sprintf(DataPrep::$activeActionDeleteBtn, 'Capitalize Words'));
        $I->wait(1);
        #re-add Suggested Activity
        $I->click(sprintf(DataPrep::$suggestedActionsElem, 'Capitalize Words'));
        $I->wait(1);
        #confirm page is not broken
        $I->seeElement(DataPrep::$suggestedActionsHeader);
        $I->seeElement(DataPrep::$activeActionsHeader);
        $I->seeElement(DataPrep::$dataPrepWorkAreaHeader);
    }

    public function clearInputFields(AcceptanceTester $I)
    {
        $I->_goToDataPrepWorkArea(ImportListType::NewList, self::$listName);
        #click column input
        $I->click(DataPrep::$columnInput);
        $I->wait(1);
        #click option
        $I->click("//div[text()='Full Name']");
        $I->wait(1);
        #cear column input
        $I->click(DataPrep::$clearColumnInputBtn);
        $I->wait(1);
        #click action input
        $I->click(DataPrep::$actionInput);
        $I->wait(1);
        #click option
        $I->click("//div[text()='Split Full Name']");
        $I->wait(1);
        #see error on the column input
        $I->seeElement(DataPrep::$columnInputError);
        #clear action input
        $I->click(DataPrep::$clearActionInputBtn);
        $I->wait(1);
        #confirm no error
        $I->dontSeeElement(DataPrep::$columnInputError);
    }
}