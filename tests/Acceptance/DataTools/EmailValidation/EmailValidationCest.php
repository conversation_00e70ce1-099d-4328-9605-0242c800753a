<?php

namespace Tests\Acceptance\DataTools\EmailValidation;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\EmailValidation as EmailValidationPage;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;

class EmailValidationCest
{
    private static $listName = 'Consumer.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'EmailValidation' . time();
        }
        return self::$projectName;
    }

    private function validateUIReport(AcceptanceTester $I, $withAlts = false)
    {
        $reportValues = $withAlts
            ? EmailValidationPage::getReportValuesWithAlts()
            : EmailValidationPage::getReportValues();

        foreach ($reportValues as $element => $expectedValue) {
            $I->assertEquals($expectedValue, $I->grabTextFrom($element));
        }
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }

    public function emailValidationList(AcceptanceTester $I)
    {
       $I->waitForElementAndClick(DashboardPage::$dataHygieneTab);
       $I->waitForElementAndClick(DashboardPage::$emailValidationCard);

       $I->doEmailValidationFlow(
           ImportListType::NewList,
           self::$listName,
           self::getProjectName(),
           ProjectType::NewProject,
           false,
       );

       $I->waitForElement(EmailValidationPage::$emailValidationFinished, 1800);
    }

    public function validateEmailValidationReport(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            basename(self::$listName, '.csv') . '-email-validation.csv',
        );

        $I->wait(1);
        $this->validateUIReport($I);
    }

    public function emailValidationListWithAlternates(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$dataHygieneTab);
        $I->waitForElementAndClick(DashboardPage::$emailValidationCard);

        $I->doEmailValidationFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject,
            true,
        );

        $I->waitForElement(EmailValidationPage::$emailValidationFinished, 1800);
    }

    public function validateEmailValidationReportWithAlternates(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            basename(self::$listName, '.csv') . '-email-validation-alternate.csv',
        );

        $I->wait(1);
        $this->validateUIReport($I, true);
    }

    // TODO: finish this.
    // public function exportList(AcceptanceTester $I)
    // {
    //     $I->openListFromProject(
    //         self::getProjectName(),
    //         basename(self::$listName, '.csv') . '-email-validation.csv',
    //     );

    //     $I->waitForElementAndClick(VisualizationPage::$secondaryResultsBtn);
    //     $I->waitForElementAndClick(VisualizationPage::$acceptTOSBtn);
    //     $I->wait(2);
    // }

    // TODO: exportedListHasData
}