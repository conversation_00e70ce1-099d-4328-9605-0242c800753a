<?php

namespace Tests\Acceptance\b2b;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Visualization as VisualizationPage;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Codeception\Attribute\Group;
use Codeception\Attribute\Depends;

class C2BAppendCest
{
    private static $listName = 'TruthSet-Internal-Sanitized.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'C2BAppend' . time();
        }
        return self::$projectName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    public function createC2BAppendFromDashboard(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$c2bAppendCard);
        $I->doC2bAppend(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject
        );
        $I->waitForElement(VisualizationPage::$c2bAppendFinished, 1800);
    }


    #[Group('sanity')]
    #[Depends('createC2BAppendFromDashboard')]
    public function exportList(AcceptanceTester $I)
    {
        $audienceExtensionListName = basename(self::$listName, '.csv') . '-c2b-append.csv';

        $I->openListFromProject(
            self::getProjectName(),
            $audienceExtensionListName
        );

        $I->waitForElementAndClick("//button[text()='Export as CSV']");
        $I->wait(2);
    }


    #[Depends('exportList')]
    public function exportedListHasData(AcceptanceTester $I)
    {
        $listName = basename(self::$listName, ".csv") . '-c2b-append.csv';

        $I->openFile($I->getDownloadDir() . $listName);
        $I->seeInThisFile('FirstName,LastName,EmailAddr,"Date of Birth",');
    }
}