<?php

namespace Tests\Acceptance\b2b;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Visualization as VisualizationsPage;
use Tests\Support\Enums\ProjectType;
use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;

class PersonaCest
{
    private static $projectName = null;
    private static $listName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'PersonaAudience' . time();
        }
        return self::$projectName;
    }

    private static function getListName()
    {
        if (self::$listName == null) {
            self::$listName = 'persona' . time();
        }
        return self::$listName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    #[Group('listgen-b2b')]
    public function createPersonaListFromDashboard(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$personaListCard);
        $I->doPersonaListFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::NewProject,
            ['Group - Agriculture (Crops)'],
            ['Owner/President'],
        );
        $I->waitForElement(VisualizationsPage::$b2bPersonaAudienceFinished, 1800);
    }

    #[Group('listgen-b2b')]
    public function createPersonaOmniCampaign(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$personaListCard);
        $I->doPersonaListFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::NewProject,
            ['Group - Agriculture (Crops)'],
            ['Owner/President'],
            'omnichannel'
        );
        $I->waitForElement(VisualizationsPage::$b2bPersonaAudienceFinished, 1800);
    }

    #[Group('sanity')]
    #[Group('listgen-b2b')]
    #[Depends('createPersonaListFromDashboard')]
    public function exportList(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::getListName() . '.csv'
        );
        $I->waitForElementAndClick("//button[text()='Export List Preview']");
        $I->wait(2);
    }


    #[Group('listgen-b2b')]
    #[Depends('exportList')]
    public function exportedListHasData(AcceptanceTester $I)
    {
        $listName = self::getListName() . '.csv';

        $I->openFile($I->getDownloadDir() . $listName);
        $I->seeInThisFile('"First Name","Last Name",Title,"Title Seniority",Department,Role,"Business Name"');
    }
}