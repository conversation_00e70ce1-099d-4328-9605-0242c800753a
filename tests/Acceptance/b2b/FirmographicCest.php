<?php

namespace Tests\Acceptance\b2b;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Visualization as VisualizationPage;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Codeception\Attribute\Group;
use Codeception\Attribute\Depends;

class FirmographicCest
{
    private static $listName = 'TS_Fortune_500.csv'; //truncated to 100 recs
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'Firmographic' . time();
        }
        return self::$projectName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    public function createFirmographicAppendFromDashboard(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$firmographicCard);
        $I->doListInsightsFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject
        );
        $I->waitForElement(VisualizationPage::$b2bFirmographicAppendFinished, 600);
    }


    #[Group('sanity')]
    #[Depends('createFirmographicAppendFromDashboard')]
    public function exportList(AcceptanceTester $I)
    {
        $audienceExtensionListName = basename(self::$listName, '.csv') . '-firmographic-append.csv';

        $I->openListFromProject(
            self::getProjectName(),
            $audienceExtensionListName
        );

        $I->waitForElementAndClick("//span[text()='Export']/parent::button");
        $I->wait(2);
    }


    #[Depends('exportList')]
    public function exportedListHasData(AcceptanceTester $I)
    {
        $headers = ['Business','Postal Address','City','State','Zip','Country','Phone','Domain','Website Home Page','Employee Count','Revenue','Year Founded','SIC','SIC Description','NAICS','NAICS Description','Public or Private'];
        $listName = basename(self::$listName, ".csv") . '-firmographic-append.csv';

        $I->openFile($I->getDownloadDir() . $listName);
        foreach ($headers as $h) {
            $I->seeInThisFile($h);
        }
    }
}