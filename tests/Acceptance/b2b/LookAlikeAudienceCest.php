<?php

namespace Tests\Acceptance\b2b;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Visualization as VisualizationsPage;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;
use Codeception\Attribute\Skip;

class LookAlikeAudienceCest
{
    private static $listName = 'test_data.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'Lookalike' . time();
        }
        return self::$projectName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Skip()]
    #[Group('sanity')]
    #[Group('lookalike')]
    public function createLookAlikeListFromDashboard(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$lookAlikeListCard);
        $I->doLookAlikeListFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject
        );
        $I->waitForText(VisualizationsPage::$b2bLookalikeAudienceFinished, 1800);
    }


    #[Group('hubspot')]
    #[Group('lookalike')]
    #[Depends('createLookAlikeListFromDashboard')]
    public function createLookAlikeFromHubspot(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$lookAlikeListCard);
        $I->doLookAlikeListFlow(
            ImportListType::HubspotList,
            self::$listName,
            self::getProjectName(),
            ProjectType::ExistingProject,
        );
        $I->waitForText(VisualizationsPage::$b2bLookalikeAudienceFinished, 1800);
    }


    #[Group('sanity')]
    #[Group('lookalike')]
    #[Depends('createLookAlikeListFromDashboard')]
    public function exportList(AcceptanceTester $I)
    {
        $audienceExtensionListName = basename(self::$listName, ".csv") . '-look-alike.csv';
        $I->openListFromProject(
            self::getProjectName(),
            $audienceExtensionListName
        );
        $I->exportList($I, 'b2b-subscription');
    }


    #[Depends('exportList')]
    #[Group('lookalike')]
    public function exportedListHasData(AcceptanceTester $I)
    {
        $listName = basename(self::$listName, ".csv") . '-look-alike.csv';
        $I->openFile($I->getDownloadDir() . $listName);
        $I->seeInThisFile('Title,"Title Seniority",Department,Role,"Business Name",Domain,"Email Address"');
    }
}