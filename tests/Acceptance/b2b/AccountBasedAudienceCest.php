<?php

namespace Tests\Acceptance\b2b;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Visualization as VisualizationPage;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Codeception\Attribute\Group;
use Codeception\Attribute\Depends;

class AccountBasedAudienceCest
{
    private static $listName = 'test_data.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'AccountBasedAudience' . time();
        }
        return self::$projectName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    public function createABMListFromDashboard(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$abmTargetedListCard);
        $I->doABMListFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject
        );
        $I->waitForElement(VisualizationPage::$b2bAccountBasedAudienceFinished, 1800);
    }

    public function createABMEmailCampaign(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$abmTargetedListCard);
        $I->doABMListFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject,
            'email'
        );
        $I->waitForElement(VisualizationPage::$b2bAccountBasedAudienceFinished, 1800);
    }

    #[Depends('createABMListFromDashboard')]
    public function createABMListFromProjectDashboard(AcceptanceTester $I)
    {
        $projectName = self::getProjectName();
        $I->openProject($projectName);
        $I->wait(1);
        $I->click("//a[text()='Business Data']");
        $I->wait(1);
        $I->click("//a[contains(@href, '/projects/create/abm-list')]");

        $I->doABMListFlow(
            ImportListType::NewList,
            self::$listName,
            $projectName,
            ProjectType::PreSelectedProject
        );
        $I->waitForElement(VisualizationPage::$b2bAccountBasedAudienceFinished, 1800);
    }


    #[Group('sanity')]
    #[Depends('createABMListFromDashboard')]
    public function exportList(AcceptanceTester $I)
    {
        $audienceExtensionListName = basename(self::$listName, '.csv') . '-abm.csv';

        $I->openListFromProject(
            self::getProjectName(),
            $audienceExtensionListName
        );

        $I->exportList($I, 'b2b-subscription');
        $I->wait(2);
    }



    #[Depends('exportList')]
    public function exportedListHasData(AcceptanceTester $I)
    {
        $listName = basename(self::$listName, ".csv") . '-abm.csv';

        $I->openFile($I->getDownloadDir() . $listName);
        $I->seeInThisFile('First Name');
    }
}