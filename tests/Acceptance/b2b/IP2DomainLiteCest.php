<?php

namespace Tests\Acceptance\b2b;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Visualization as VisualizationPage;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;
use Codeception\Attribute\Skip;

class IP2DomainLiteCest
{
    private static $listName = 'IP-List.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'IP2DomainLite' . time();
        }
        return self::$projectName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    public function createIP2DomainLiteFromDashboard(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$ip2DomainLiteCard);
        $I->doListInsightsFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject
        );
        $I->waitForElement(VisualizationPage::$ip2DomainLiteFinished, 1800);
    }


    #[Group('sanity')]
    #[Depends('createIP2DomainLiteFromDashboard')]
    public function exportList(AcceptanceTester $I)
    {
        $audienceExtensionListName = basename(self::$listName, '.csv') . '-ip-domain-append-lite.csv';
        $I->openListFromProject(
            self::getProjectName(),
            $audienceExtensionListName
        );

        $I->waitForElementAndClick("//span[text()='Export']/parent::button");
        $I->wait(2);
    }


    #[Depends('exportList')]
    public function exportedListHasData(AcceptanceTester $I)
    {
        $listName = basename(self::$listName, ".csv") . '-ip-domain-append-lite.csv';
        $I->openFile($I->getDownloadDir() . $listName);
        $I->seeInThisFile('"Is ISP?","Domain 1","Domain 2","Domain 3"');
    }
}