<?php

namespace Tests\Acceptance\b2b;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Project as ProjectPage;
use Tests\Support\Page\Visualization as VisualizationsPage;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Tests\Support\Enums\ListAction;
use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;

class ListInsightsCest
{
    private static $listName = 'test_data.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'ListInsights' . time();
        }
        return self::$projectName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    public function createListInsightsFromMainDashboard(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$listInsightsCard);
        $I->doListInsightsFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject
        );
        $I->waitForElement(VisualizationsPage::$revenueCardHeader, 500);
    }


    #[Group('sanity')]
    #[Group('hubspot')]
    #[Depends('createListInsightsFromMainDashboard')]
    public function createListInsightsFromMainDashboardImportHubSpot(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$listInsightsCard);
        $I->doListInsightsFlow(
            ImportListType::HubspotList,
            $I::HUBSPOT_LIST_NAME,
            self::getProjectName(),
            ProjectType::ExistingProject
        );
        $I->wait(2);
    }

    #[Depends('createListInsightsFromMainDashboard')]
    public function verifyListActionMenu(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$listName
        );

        $listCardLocator = sprintf(ProjectPage::$listLocatorFormatString, self::$listName);
        $recordCount = $listCardLocator . '/following-sibling::div[1]';
        $listType = $listCardLocator . '/following-sibling::div[2]';

        // $I->assertEquals(self::$listName, $I->grabTextFrom(self::$listName), 'Displayed list name is not same as uploaded list name');
        // $I->assertEquals('42 Records', $I->grabTextFrom($recordCount), 'Displayed record count does not match expected');
        // $I->assertEquals('Original List', $I->grabTextFrom($listType), 'Displayed list type does not match expected');

        $listActionButton = $listCardLocator . '/parent::div/parent::div/parent::a/following-sibling::button';
        // open actions menu
        $I->click($listActionButton);
        $I->wait(2);
        // close actions menu
        $I->click($listActionButton);
        $I->wait(2);
        // open actions menu again
        $I->click($listActionButton);
        $I->wait(2);
    }


    #[Group('sanity')]
    #[Depends('createListInsightsFromMainDashboard')]
    public function verifyListInsights(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$listName
        );
        $I->validateVisualizations($I::B2B);
    }


    #[Group('sanity')]
    #[Depends('createListInsightsFromMainDashboard')]
    public function exportListInsightsList(AcceptanceTester $I)
    {
        $I->doListAction(
            self::getProjectName(),
            self::$listName,
            ListAction::Export
        );
        $I->wait(3);
    }


    #[Depends('createListInsightsFromMainDashboard')]
    public function shareInsights(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$listName
        );
        // wait for insights to be fully loaded
        $I->waitForElementClickable(VisualizationsPage::$shareInsightsBtn);
        // send invite
        $I->click(VisualizationsPage::$shareInsightsBtn);
        $I->waitForText(VisualizationsPage::$shareInsightsText);
        $I->fillField(VisualizationsPage::$sendInviteInput, '<EMAIL>');
        $I->waitForElementAndClick(VisualizationsPage::$sendInviteBtn);
        $I->waitForText(VisualizationsPage::$successToastText);
    }


    #[Group('sanity')]
    #[Depends('createListInsightsFromMainDashboard')]
    public function downloadVisualizations(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$listName
        );

        $I->wait(3);

        $I->moveMouseOver(VisualizationsPage::$downloadAsPDFBtn);
        $I->wait(1);
        $I->waitForText(VisualizationsPage::$PDFDownloadReadyText);

        $I->moveMouseOver(VisualizationsPage::$downloadAsPNGBtn);
        $I->wait(1);
        $I->waitForText(VisualizationsPage::$PNGDownloadReadyText);

        $I->waitForElementAndClick(VisualizationsPage::$downloadAsPDFBtn);
        $I->wait(1);
        $I->waitForElementAndClick(VisualizationsPage::$downloadAsPNGBtn);
        $I->wait(1);
    }

     //************************************* */
    //TODO: What to do with these tests? Move them into other tests suites?

    /**
     * @depends createListInsightsFromMainDashboard
     * @group sanity
     */
    // public function createOnlineAudienceFromListActions(AcceptanceTester $I)
    // {
    //     $I->doListAction(
    //         self::getProjectName(),
    //         self::$listName,
    //         $I::ONLINE_AUDIENCE
    //     );
    //     $I->listInsightsFlow(
    //         $I,
    //         self::getProjectName(),
    //         self::$listName,
    //         $I::ONLINE_AUDIENCE,
    //         $I::PRE_SELECTED_PROJECT_NAME,
    //         $I::USE_EXISTING_LIST
    //     );
    //     $I->waitForElement(VisualizationsPage::$b2bOnlineAudienceFinished, 1800);
    // }

    /**
     * @depends createListInsightsFromMainDashboard
     * @group sanity
     */
    // public function createLookAlikeAudienceFromListActions(AcceptanceTester $I)
    // {
    //     $I->doListAction(
    //         self::getProjectName(),
    //         self::$listName,
    //         $I::LOOK_ALIKE_AUDIENCE
    //     );
    //     $I->listInsightsFlow(
    //         $I,
    //         self::getProjectName(),
    //         self::$listName,
    //         $I::LOOK_ALIKE_AUDIENCE,
    //         $I::PRE_SELECTED_PROJECT_NAME,
    //         $I::USE_EXISTING_LIST
    //     );
    //     $I->waitForText(VisualizationsPage::$b2bLookalikeAudienceFinished, 1800);
    // }

    /**
     * @depends createListInsightsFromMainDashboard
     * @group sanity
     */
    // public function createAccountBasedAudienceFromListActions(AcceptanceTester $I) {
    //     $I->doListAction(
    //         self::getProjectName(),
    //         self::$listName,
    //         $I::ACCOUNT_BASED_AUDIENCE
    //     );
    //     $I->listInsightsFlow(
    //         $I,
    //         self::getProjectName(),
    //         self::$listName,
    //         $I::ACCOUNT_BASED_AUDIENCE,
    //         $I::PRE_SELECTED_PROJECT_NAME,
    //         $I::USE_EXISTING_LIST
    //     );
    //     $I->waitForElement(VisualizationsPage::$b2bAccountBasedAudienceFinished, 1800);
    // }
}
