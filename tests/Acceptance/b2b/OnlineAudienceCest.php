<?php

namespace Tests\Acceptance\b2b;

use \Tests\Support\AcceptanceTester;
use \Tests\Support\Page\Dashboard as DashboardPage;
use \Tests\Support\Page\Visualization as VisualizationPage;
use \Tests\Support\Enums\ImportListType;
use \Tests\Support\Enums\ProjectType;
use Codeception\Attribute\Group;
use Codeception\Attribute\Depends;

class OnlineAudienceCest
{
    private static $listName = 'test_data.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'OnlineAudience' . time();
        }
        return self::$projectName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }

    #[Group('sanity')]
    public function createOnlineAudienceFromDashboard(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$onlineAudienceCard);
        $I->doListInsightsFlow(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            ProjectType::NewProject,
        );
        $I->waitForElement(VisualizationPage::$b2bOnlineAudienceFinished, 1800);
    }


    #[Group('sanity')]
    #[Depends('createOnlineAudienceFromDashboard')]
    public function exportOnlineAudienceList(AcceptanceTester $I)
    {
        $audienceExtensionListName = basename(self::$listName, ".csv") . '-audience-extension.csv';
        $I->openListFromProject(
            self::getProjectName(),
            $audienceExtensionListName
        );

        # click Export
        $I->waitForElementAndClick("//span[text()='Export']/parent::button");
        # click Export button
        $I->waitForElementAndClick(VisualizationPage::$confirmExportBtn);
        # agree to terms
        $I->waitForElementAndClick(VisualizationPage::$acceptTOSBtn);

        $I->wait(3);
    }
}