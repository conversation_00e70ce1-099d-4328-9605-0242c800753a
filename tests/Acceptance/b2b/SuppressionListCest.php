<?php

namespace Tests\Acceptance\b2b;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\AccountSettings as AccountSettingsPage;
use Tests\Support\Page\ListInsights as ListInsightsPage;
use Tests\Support\Page\Visualization as VisualizationPage;
use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;

class SuppressionListCest
{
    private static $list = 'sample-file.csv';
    private static $suppressionList = 'sample-file-email-only.csv';
    private static $symbolList = 'QA-automation-symbols.csv';

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }

    private static function _addSuppressionList(AcceptanceTester $I, $listName)
    {
        $I->waitForElementAndClick(DashboardPage::$accountSettingsButton);
        $I->waitForElementAndClick(DashboardPage::$accountSettingsLink);
        $I->waitForElementAndClick(AccountSettingsPage::$suppressionAndOptOutLink);
        $I->waitForText('Suppression Lists');
        $I->wait(1);

        $I->attachFile(AccountSettingsPage::$suppressionListInput, $listName);
        $I->waitForText('List imported!');
        $I->waitForElementAndClick("//button/span[text()='Next Step']");
        $I->waitForElementAndClick("//button/span[text()='Import List']");
        $I->waitForText('Import complete');
        $I->wait(2);
        // assert that we see the suppresion list name
        $I->waitForText(basename($listName, '.csv'));

        $I->waitForElementAndClick(AccountSettingsPage::$suppresionListActionBtn);
        $I->canSeeElements(AccountSettingsPage::getActionListBtns());
    }

    private static function _runJobWithSuppressionList(AcceptanceTester $I, $listName)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$onlineAudienceCard);
        $I->wait(2);
        $I->attachFile(ListInsightsPage::$listInput, $listName);
        $I->waitForText(ListInsightsPage::$uploadCompleteString, 60);
        $I->scrollTo(ListInsightsPage::$importListNextStepButton);
        $I->waitForElementAndClick(ListInsightsPage::$importListNextStepButton);

        $I->waitForText(sprintf(ListInsightsPage::$mapInputsHeaderFormatString, $listName));
        $I->scrollTo(ListInsightsPage::$mapFieldsNextStepButton);
        $I->waitForElementAndClick(ListInsightsPage::$mapFieldsNextStepButton);

        $I->waitForText('All suppression lists are active.');
        $I->pressKey(
            ListInsightsPage::$projectNameInput,
            'suppression-list' . time(),
            \Facebook\WebDriver\WebDriverKeys::ENTER
        );
        $I->waitForElementAndClick(ListInsightsPage::$createButton);
    }

    private static function _deleteSuppressionList(AcceptanceTester $I, $listName)
    {
        $I->waitForElementAndClick(DashboardPage::$accountSettingsButton);
        $I->waitForElementAndClick(DashboardPage::$accountSettingsLink);
        $I->waitForElementAndClick(AccountSettingsPage::$suppressionAndOptOutLink);
        $I->waitForText('Suppression Lists');

        $I->waitForElementAndClick(AccountSettingsPage::$suppresionListActionBtn);
        $I->waitForElementAndClick(AccountSettingsPage::$deleteActionBtn);

        $I->waitForText('Are you sure you want to delete this suppression?');
        $I->waitForElementAndClick(AccountSettingsPage::$confirmDeleteBtn);
        $I->wait(1);
        // assert the list is gone
        $I->dontSee(basename($listName, '.csv'));
        $I->dontSee(AccountSettingsPage::$suppresionListActionBtn);
    }


    #[Group('sanity')]
    public function addSuppressionList(AcceptanceTester $I)
    {
        self::_addSuppressionList($I, self::$suppressionList);
    }


    #[Group('sanity')]
    #[Depends('addSuppressionList')]
    public function runJobWithSuppressionList(AcceptanceTester $I)
    {
        self::_runJobWithSuppressionList($I, self::$list);
        $I->waitForText('No Records Found', 120);
    }


    #[Group('sanity')]
    #[Depends('addSuppressionList')]
    public function deleteSuppressionList(AcceptanceTester $I)
    {
        self::_deleteSuppressionList($I, self::$suppressionList);
    }


    public function addSupressionListSymbolCharacters(AcceptanceTester $I)
    {
        self::_addSuppressionList($I, self::$symbolList);
    }


    #[Depends('addSupressionListSymbolCharacters')]
    public function runJobWithSuppressionListSymbolCharacters(AcceptanceTester $I)
    {
        self::_runJobWithSuppressionList($I, self::$symbolList);
        $I->waitForElement(VisualizationPage::$b2bOnlineAudienceFinished, 120);
    }


    #[Depends('addSupressionListSymbolCharacters')]
    public function deleteSuppressionListSymbolCharacters(AcceptanceTester $I)
    {
        self::_deleteSuppressionList($I, self::$symbolList);
    }
}