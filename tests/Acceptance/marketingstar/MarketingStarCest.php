<?php

namespace Tests\Acceptance\marketingstar;

use Codeception\Attribute\Group;
use Codeception\Attribute\Before;
use Codeception\Attribute\After;
use Tests\Support\AcceptanceTester;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Tests\Support\Page\B2CAttributeSelection as AttributeSelectPage;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Visualization as VisualizationPage;
use Tests\Support\Page\MarketingStar as MarketingStarPage;

class MarketingStarCest
{
    private static $listName = 'consumer-no-phone.csv'; //TODO: need a M* compliant b2c contact append list; Keep it small & fast.
    private static $reachProjectName = null;

    private static function getProjectName()
    {
        if (self::$reachProjectName == null) {
            self::$reachProjectName = 'MarketingStarUpload-' . time();
        }
        return self::$reachProjectName;
    }

    public function loginReach(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword(), true);
        $I->dismissAppVersionToast($I);
    }

    public function logoutReach(AcceptanceTester $I)
    {
        $I->logout();
    }

    public function loginMarketingStar(AcceptanceTester $I)
    {
        //TODO
    }

    public function logoutMarketingStar(AcceptanceTester $I)
    {
        //TODO
    }

    #[Before('loginReach')]
    #[After('logoutReach')]
    public function uploadListToMarketingStar(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$contactAppendCard);
        $I->waitForText('Create a Contact Append'); // Throw this in a var

        $I->doB2cContactAppend(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            [AttributeSelectPage::$phoneCheckBoxLabel],
            ProjectType::NewProject,
        );
        $I->waitForElement(VisualizationPage::$b2cContactAppendFinishedElem, 1800);

        # export the list
        $I->waitForElementAndClick(VisualizationPage::$primaryResultsBtn);
        $I->waitForElementAndClick(VisualizationPage::$exportToMarketingStarCheckbox);
        $I->waitForElementAndClick(VisualizationPage::$exportAgreeBtn);
        # check for success toast
        $I->waitForText('Marketing Star list upload started successfully.');
        # mouse over upload icon and verify text
        $I->moveMouseOver("//img[@alt='Marketing Star logo']/parent::div");
        $I->waitForText('Your upload is processing. Please wait.');

        $I->wait(10);
    }

    public function verifyListInMarketingStar(AcceptanceTester $I)
    {
        //TODO
    }
}