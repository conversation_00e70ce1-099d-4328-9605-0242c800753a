<?php

namespace Tests\Acceptance\general;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Login as LoginPage;
use Codeception\Attribute\Group;

class LoginCest
{
    #[Group('sanity')]
    public function verifyLoginPage(AcceptanceTester $I)
    {
        $I->amOnPage(LoginPage::$URL);
        $I->wait(1);
        $I->seeInTitle(LoginPage::$title);

        // verify the expected text is present on the log-in page
        $I->canSeeStrings(LoginPage::getStrings());
        $I->canSeeElements(LoginPage::getElements());
        $I->canSeeLinks(LoginPage::getLinks());
    }
}
