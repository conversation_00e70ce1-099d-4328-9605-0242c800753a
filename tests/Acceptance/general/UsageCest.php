<?php

namespace Tests\Acceptance\general;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Usage as UsagePage;
use Tests\Support\Page\SideNav;
use Codeception\Attribute\Group;

class UsageCest
{
    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    public function verifyUsageStats(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(SideNav::$usageBtn);
        $I->waitForText(UsagePage::$headerText);

        //verify the 5 usage stats are displayed
        $I->canSeeElement(UsagePage::$nextBillingDate);
        $I->canSeeElement(UsagePage::$nextCreditDistribution);
        $I->canSeeElement(UsagePage::$creditsPerCycle);
        $I->canSeeElement(UsagePage::$contractStartDate);
        $I->canSeeElement(UsagePage::$accountCreationDate);
    }

    #[Group('sanity')]
    public function verifyUsageFilters(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(SideNav::$usageBtn);
        $I->waitForText(UsagePage::$headerText);

        //can see all 3 usage filters
        $I->canSeeElement(UsagePage::$apiTypeSelect);
        $I->canSeeElement(UsagePage::$apiKeysSelect);
        $I->canSeeElement(UsagePage::$dataToolsSelect);

        //select List/Audience; should only see DataTool filter
        $I->selectOption(UsagePage::$queryTypeSelect, 'List / Audience');
        $I->wait(1);
        $I->canSeeElement(UsagePage::$dataToolsSelect);
        $I->dontSeeElement(UsagePage::$apiTypeSelect);
        $I->dontSeeElement(UsagePage::$apiKeysSelect);

        //select API;, should only see apiType & apiKeys filters
        $I->selectOption(UsagePage::$queryTypeSelect, 'API');
        $I->wait(1);
        $I->canSeeElement(UsagePage::$apiTypeSelect);
        $I->canSeeElement(UsagePage::$apiKeysSelect);
        $I->dontSeeElement(UsagePage::$dataToolsSelect);
    }
}