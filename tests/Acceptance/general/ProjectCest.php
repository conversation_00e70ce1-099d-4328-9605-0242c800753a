<?php

namespace Tests\Acceptance\general;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\SideNav;
use Tests\Support\Page\ProjectsList as ProjectsListPage;
use Tests\Support\Page\Project as ProjectPage;
use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;
use Codeception\Attribute\Skip;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;

class ProjectCest
{
    private static $projectName = null;
    private static $projName1 = null;
    private static $projName2 = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'Project' . time();
        }
        return self::$projectName;
    }
    private static function getProjName1()
    {
        if (self::$projName1 == null) {
            self::$projName1 = 'ProjectTestDir1-' . time();
        }
        return self::$projName1;
    }
    private static function getProjName2()
    {
        if (self::$projName2 == null) {
            self::$projName2 = 'ProjectTestDir2-' . time();
        }
        return self::$projName2;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    public function createProject(AcceptanceTester $I)
    {
        $projectName = self::getProjectName();

        $I->createProject($projectName);

        //go back to projects page, verify new project is created
        $I->waitForElementAndClick(SideNav::$projectsLink);
        $I->waitForText($projectName);
    }


    #[Group('sanity')]
    #[Depends('createProject')]
    public function searchForProject(AcceptanceTester $I)
    {
        $projectName = self::getProjectName();

        $I->openProject();

        // search with full name, assert that there is only one result
        $I->fillField(ProjectsListPage::$searchInput, $projectName);
        $I->wait(2);
        $tableRows = $I->grabMultiple(ProjectsListPage::$projectNameLocator);

        // only element should be in the table
        $I->assertEquals(1, sizeof($tableRows));
        // and that element is the name of the  campaign
        $I->assertEquals($projectName, $tableRows[0]);
    }


    #[Group('sanity')]
    public function sortProjects(AcceptanceTester $I)
    {
        $columnIndexMap = ProjectsListPage::getColumnIndexMap();

        $I->openProject();

        foreach ($columnIndexMap as $sortKey => $columnIndx) {
            $sortKeyElem = sprintf(ProjectsListPage::$sortIconLocatorString, $sortKey);
            $fieldNameLocator = sprintf(ProjectsListPage::$fieldNameLocatorFormatString, $columnIndx);

            # Determine if this field should use numeric comparison
            $isNumeric = in_array($sortKey, [ProjectsListPage::$listsCountSortKey, ProjectsListPage::$listsProcessingSortKey]);

            # click the sortKey (elements should now be sorted in ascending order)
            $I->waitForElementAndClick($sortKeyElem);
            $I->wait(1);
            # grab array of fields names
            $fieldNames = $I->grabMultiple($fieldNameLocator);
            # assert they're sorted
            $I->assertTrue($I->isArraySorted($fieldNames, 'asc', $isNumeric), "Elements are not sorted in ascending order for $sortKey");
            # click sortKey again (elements should now be in descending order)
            $I->waitForElementAndClick($sortKeyElem);
            $I->wait(1);
            $fieldNames = $I->grabMultiple($fieldNameLocator);
            $I->assertTrue($I->isArraySorted($fieldNames, 'desc', $isNumeric), "Elements are not sorted in descending order for $sortKey");
        }
    }


    #[Group('sanity')]
    #[Depends('createProject')]
    public function deleteProject(AcceptanceTester $I)
    {
        $localProjectName = self::getProjectName();

        $I->openProject();
        # filter by project name
        $I->fillField(ProjectsListPage::$searchInput, $localProjectName);
        $I->wait(2);
        # click actions button
        $I->waitForElementAndClick("//button[@class='btn btn-secondary bg-transparent border-0 rounded-0 text-reset']");
        # click delete
        $I->waitForElementAndClick("//button[text()='Delete']");
        # confirm delete
        $I->waitForElementAndClick(ProjectsListPage::$finalDeleteButton);
        $I->wait(3);

        $I->dontSee($localProjectName);
    }

    #[Group('moveList')]
    public function setupMoveList(AcceptanceTester $I)
    {
        $projName = self::getProjName1();

        $I->createProject($projName);
        $I->waitForElementAndClick("//a[contains(@href, '/projects/create/b2c-list-insights')]");
        $I->doListInsightsFlow(
            ImportListType::NewList,
            'test_data.csv',
            $projName,
            ProjectType::PreSelectedProject
        );
        $I->wait(2);
    }

    #[Group('moveList')]
    #[Depends('setupMoveList')]
    public function moveListToNewProject(AcceptanceTester $I)
    {
        $projName1 = self::getProjName1();
        $projName2 = self::getProjName2();

        $I->openProject();
        // search for ProjectTestDir1
        $I->fillField(ProjectsListPage::$searchInput, $projName1);
        // click project
        $I->waitForTextAndClick($projName1);
        // click action button
        $I->waitForElementAndClick(ProjectPage::$actionMenuBtn);
        // click move
        $I->waitForElementAndClick(ProjectPage::$moveListActionBtn);
        // Make assertions about Move List modal
        $I->waitForText(ProjectPage::$moveListModalText);
        // Enter ProjectTestDir1 -> Enter
        $I->pressKey(
            ProjectPage::$moveListModalInput,
            $projName2,
            \Facebook\WebDriver\WebDriverKeys::ENTER
        );
        // Click submit
        $I->waitForElementAndClick(ProjectPage::$moveListModalSubmitBtn);
        // Assert we are in the new project
        $I->waitForElement(sprintf(ProjectPage::$projectHeaderFormatString, $projName2));
        $I->waitForElement(sprintf(ProjectPage::$projectBreadCrumbFormatString, $projName2));
    }

    #[Group('moveList')]
    #[Depends('setupMoveList')]
    public function moveListToExistingProject(AcceptanceTester $I)
    {
        $projName1 = self::getProjName1();
        $projName2 = self::getProjName2();

        $I->openProject();
        // search for ProjectTestDir2
        $I->fillField(ProjectsListPage::$searchInput, $projName2);
        // click project
        $I->waitForTextAndClick($projName2);
        // click action button
        $I->waitForElementAndClick(ProjectPage::$actionMenuBtn);
        // click move
        $I->waitForElementAndClick(ProjectPage::$moveListActionBtn);
        // Make assertions about Move List modal
        $I->waitForText(ProjectPage::$moveListModalText);
        // select option
        $I->pressKey(
            ProjectPage::$moveListModalInput,
            $projName1,
            \Facebook\WebDriver\WebDriverKeys::ENTER
        );
        // click submit
        $I->waitForElementAndClick(ProjectPage::$moveListModalSubmitBtn);
        // Assert we are in sample project
        $I->waitForElement(sprintf(ProjectPage::$projectHeaderFormatString, $projName1));
        $I->waitForElement(sprintf(ProjectPage::$projectBreadCrumbFormatString, $projName1));
    }


    #[Group('sanity')]
    function pagination(AcceptanceTester $I) {
        # Assumes 10+ projects to work!
        $I->openProject();

        $I->waitForElement(ProjectsListPage::$resultsTextElem);

        # the default pagination size is 10, so there should be 10 rows
        $I->seeNumberOfElements("//tbody/tr", 10);

        # change the pagination size to 25
        $I->waitForElementAndClick(ProjectsListPage::$paginationDropdown);
        $I->waitForElementAndClick(ProjectsListPage::$show25PaginationBtn);
        $I->wait(1);

        # there should be between 11 and 25 rows
        $I->seeNumberOfElements("//tbody/tr", [11,25]);

        # change the pagination size back to 10
        $I->scrollTo(ProjectsListPage::$paginationDropdown);
        $I->waitForElementAndClick(ProjectsListPage::$paginationDropdown);
        $I->waitForElementAndClick(ProjectsListPage::$show10PaginationBtn);
        $I->wait(1);

        # there should be 10 rows
        $I->seeNumberOfElements("//tbody/tr", 10);

        # click next page
        $I->waitForElementAndClick(ProjectsListPage::$nxtPageBtn);
        # verify page 2 is active
        $I->waitForElement(ProjectsListPage::$page2LinkActive);
        # click previous page
        $I->waitForElementAndClick(ProjectsListPage::$previousPageBtn);
        # verify page 1 is active
        $I->waitForElement(ProjectsListPage::$page1LinkActive);
    }


    // #[Group('cleanup')]
    // private function deleteAllProjects(AcceptanceTester $I) {
    //     $I->openProject();
    //     $projectSortKeys = ProjectsListPage::getProjectSortKeys();

    //     $i = 2;

    //     // sort by the date created
    //     //$sortKeyLocator = sprintf(ProjectsListPage::$sortIconLocatorString, $projectSortKeys[$i]);
    //     //$I->waitForElementAndClick($sortKeyLocator);

    //     $projectFieldLocator = sprintf(ProjectsListPage::$projectFieldLocatorFormatString, $i);

    //     // clicking first time, the elements are sorted in ascending order

    //     for ($i = 0; $i < 200; $i++) {
    //         $projectFieldValues = $I->grabMultiple($projectFieldLocator);

    //         foreach ($projectFieldValues as $localProjectName) {
    //             $I->click(sprintf(ProjectsListPage::$projectOptionsButtonFormatString, $localProjectName));

    //             $deleteButton = sprintf(ProjectsListPage::$deleteProjectButtonFormatString, $localProjectName);
    //             $I->waitForElementAndClick($deleteButton);

    //             $I->waitForElementAndClick(ProjectsListPage::$finalDeleteButton);
    //             $I->wait(1);

    //             // $I->dontSee($localProjectName);
    //         }
    //     }
    // }
}