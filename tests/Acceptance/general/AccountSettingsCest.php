<?php

namespace Tests\Acceptance\general;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\AccountSettings as AccountSettingsPage;
use Tests\Support\Page\Dashboard as DashboardPage;
use Codeception\Attribute\Group;

class AccountSettingsCest
{
    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    public function verifyAccountSettings(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$accountSettingsButton);
        $I->seeLink('Settings', 'account');
        $I->click('Settings');

        $I->seeCurrentUrlEquals('/account/profile');

        $I->canSeeElements(AccountSettingsPage::getAccountSettingsLinks());
    }
}
