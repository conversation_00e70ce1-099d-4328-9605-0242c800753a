<?php

namespace Tests\Acceptance\b2b;

use Tests\Support\AcceptanceTester;
use Tests\Support\Page\Dashboard as DashboardPage;
use Codeception\Attribute\Group;
use Codeception\Attribute\Skip;
use Tests\Support\Page\SideNav as SideNavPage;

class DashboardCest
{
    public function _before(AcceptanceTester $I)
    {
        $I->login(
            $I->getEmail(),
            $I->getPassword(),
            // true
        );
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }

    /**
     * Click through operations on the dashboard
     */
    #[Group('sanity')]
    public function verifyDashboardOperations(AcceptanceTester $I)
    {
        # click through cards, starting with consumer tools
        foreach (DashboardPage::consumerOpsAndBreadcrumbs() as $cardElem => $breadCrumb) {
            $I->waitForElementAndClick($cardElem);
            $I->waitForText($breadCrumb);
            $I->waitForElementAndClick(DashboardPage::$homeLink);
        };

        # click through business tools
        foreach (DashboardPage::bussinessOpsAndBreadcrumbs() as $cardElem => $breadCrumb) {
            # click the Business Tools tab
            $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
            $I->wait(1);
            # begin clicking through the cards
            $I->waitForElementAndClick($cardElem);
            $I->waitForText($breadCrumb);
            $I->waitForElementAndClick(DashboardPage::$homeLink);
        }

        # data hygiene tools
        foreach (DashboardPage::dataHygieneAndBreadcrumbs() as $cardElem => $breadCrumb) {
            # click the Data Hygiene tab
            $I->waitForElementAndClick(DashboardPage::$dataHygieneTab);
            $I->wait(1);
            # begin clicking through the cards
            $I->waitForElementAndClick($cardElem);
            $I->waitForText($breadCrumb);
            $I->waitForElementAndClick(DashboardPage::$homeLink);
        }
    }

    /**
     * Verify the dashboard's side navigation
     */
    public function verifySideNav(AcceptanceTester $I)
    {
        $I->canSeeElements(SideNavPage::getSideNavElems());
    }

    /**
     * Verify the Announcements Card
     */
    public function verifyAnnouncements(AcceptanceTester $I)
    {
        $I->canSeeElements(DashboardPage::getAnnouncementElems());
    }

    /**
     * Verify the Quick Resources Card
     */
    public function verifyQuickResources(AcceptanceTester $I)
    {
        # first link is open by default
        $I->canSeeElement(DashboardPage::$quickResourcesHubspotLink);

        # open and verify the remaining accordion contents
        foreach (DashboardPage::getQuickResourcesElems() as $header => $link) {
            # click accordion header
            $I->waitForElementAndClick(sprintf(DashboardPage::$accordianHeaderFormatString, $header));
            # verify link
            $I->canSeeElement($link);
        }
    }

    /**
     * Click through the M* Card
     * (M* dashboard at "/?mStarPromo")
     */
    public function verifyMarketingStarPromo(AcceptanceTester $I)
    {
        $I->seeElement(DashboardPage::$marketingStarLogo);
        $I->waitForElementAndClick(DashboardPage::$marketingStarCard);
        # opens a new tab to Marketing Star
        $I->switchToNextTab();
        # Verify we see the Marketing Star logo
        $I->seeElement("//h5[text()='Set Password & Activate Account']");
        # close M* and switch back to Reach
        $I->closeTab();
        $I->wait(1);
        # logout
    }

    /**
     * Verify App Version name/number
     */
    public function verifyVersion(AcceptanceTester $I)
    {
        $versionFromPage = $I->grabTextFrom(DashboardPage::$appVersionElement);
        $I->assertEquals(DashboardPage::$appVersion, $versionFromPage, "App Version number mismatch");
        $I->moveMouseOver(DashboardPage::$appVersionElement);
        $I->waitForText(DashboardPage::$appVersionText);
        $I->wait(1);
    }


    public function verifyUsageWidget(AcceptanceTester $I)
    {
        $I->waitForElementVisible(DashboardPage::$usageSnapshotSVG);
        $I->canSee(DashboardPage::$usageSnaphotTitle);
        $I->canSeeElement(DashboardPage::$usageSnapshotMatchesUsed);
        $I->wait(1);

        $I->moveMouseOver(DashboardPage::$usageSnapshotInfoSVG);
        $I->canSee('Billing Period:');
    }


    public function verifyAccountInfo(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$accountSettingsButton);
        $I->canSeeStrings(DashboardPage::getAccountInfoStrings());
        //close settings
        $I->click(DashboardPage::$accountSettingsButton);
    }
}