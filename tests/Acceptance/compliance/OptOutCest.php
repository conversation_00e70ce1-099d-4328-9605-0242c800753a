<?php

namespace Tests\Acceptance\customer;

use Tests\Support\AcceptanceTester;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
// use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\DashboardOld as DashboardPage;
use Tests\Support\Page\ProjectsList as ProjectsPage;
use Tests\Support\Page\B2CAttributeSelection as AttributeSelectPage;
use Tests\Support\Page\SideNav;
use Tests\Support\Enums\MatchType;
use Codeception\Attribute\Group;
use Codeception\Attribute\Skip;

class OptOutCest
{
    private static $optOutFileName = 'optout.csv';

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->wait(1);
        $I->logout();
    }

    private function getProjectName()
    {
        return 'OptOut-' . date('Y-m-d');
    }

    # create project to hold lists
    #[Group('optout')]
    public function create_project(AcceptanceTester $I)
    {
        # copy and pasted from CustomerCest, how is this not a reusable function?
        $I->waitForElementAndClick(SideNav::$projectsLink);
        $I->wait(1);
        $I->waitForElementAndClick(ProjectsPage::$createProjectButton);
        $I->waitForElement(ProjectsPage::$newProjectInput);
        $I->fillField(ProjectsPage::$newProjectInput, self::getProjectName());
        $I->waitForElementAndClick(ProjectsPage::$createButton);
    }

    #[Group('optout')]
    public function demographic(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$demographicAppendCard);
        $I->doB2cDemographicAppend(
            ImportListType::NewList,
            self::$optOutFileName,
            self::getProjectName(),
            [
                AttributeSelectPage::$basicDemographicCheckboxLabel,
                AttributeSelectPage::$finAutoDemographicCheckboxLabel,
                AttributeSelectPage::$lifestyleDemographicCheckboxLabel,
                AttributeSelectPage::$policatlDemographicCheckboxLabel
            ],
            ProjectType::ExistingProject,
        );
    }

    #[Group('optout')]
    public function online_audience(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$b2cOnlineAudienceCard);
        $I->doListInsightsFlow(
            ImportListType::ExistingList,
            self::$optOutFileName,
            self::getProjectName(),
            ProjectType::PreSelectedProject,
        );
    }

    #[Group('optout')]
    public function contact_append_address_hh(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$contactAppendCard);
        $I->doB2cContactAppend(
            ImportListType::ExistingList,
            self::$optOutFileName,
            self::getProjectName(),
            [AttributeSelectPage::$addressCheckBoxLabel],
            ProjectType::PreSelectedProject,
            null,
            MatchType::HouseHold
        );
    }

    #[Group('optout')]
    public function contact_append_email_hh(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$contactAppendCard);
        $I->doB2cContactAppend(
            ImportListType::ExistingList,
            self::$optOutFileName,
            self::getProjectName(),
            [AttributeSelectPage::$emailCheckBoxLabel],
            ProjectType::PreSelectedProject,
            null,
            MatchType::HouseHold
        );
    }

    #[Group('optout')]
    public function contact_append_phone_mobile_hh(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$contactAppendCard);
        $I->doB2cContactAppend(
            ImportListType::ExistingList,
            self::$optOutFileName,
            self::getProjectName(),
            [AttributeSelectPage::$phoneCheckBoxLabel],
            ProjectType::PreSelectedProject,
            AttributeSelectPage::$phoneMobileLabel,
            MatchType::HouseHold
        );
    }

    #[Group('optout')]
    public function contact_append_phone_multi_hh(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$contactAppendCard);
        $I->doB2cContactAppend(
            ImportListType::ExistingList,
            self::$optOutFileName,
            self::getProjectName(),
            [AttributeSelectPage::$phoneCheckBoxLabel],
            ProjectType::PreSelectedProject,
            AttributeSelectPage::$phoneMultiLabel,
            MatchType::HouseHold
        );
    }

    #[Group('optout')]
    public function c2b_append(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$businessToolsTab);
        $I->waitForElementAndClick(DashboardPage::$c2bAppendCard);
        $I->doC2bAppend(
            ImportListType::NewList,
            self::$optOutFileName,
            self::getProjectName(),
            ProjectType::ExistingProject,
            true // exclude address fields when mapping
        );
    }
}