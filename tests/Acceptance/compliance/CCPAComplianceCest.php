<?php

namespace Tests\Acceptance\b2c;

use Tests\Support\AcceptanceTester;
use Tests\Support\Enums\ContactType;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Visualization as VisualizationPage;
use Codeception\Attribute\Group;
use Codeception\Attribute\Before;
use Codeception\Attribute\After;
use Codeception\Attribute\Depends;
use Tests\Support\Classes\ListGenAttrConfig;
use Tests\Support\Classes\ListGenContact;
use Tests\Support\Classes\ListGenGeoFilters;
use Tests\Support\Enums\DemographicType;
use Codeception\Attribute\Skip;
use Tests\Support\Enums\ProjectType;

class CCPAComplianceCest {

    private static $projectName = null;
    private static $complianceListName = 'listgen-automation-ccpa';

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'CCPA-Compliance' . time();
        }
        return self::$projectName;
    }

    private function login(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    private function logout(AcceptanceTester $I)
    {
        $I->logout();
    }

    private static function checkComplianceErrors(AcceptanceTester $I, $header, $rec)
    {
        # Gender used to be a field we checked, but now its allowed.
        $fullRecord = array_combine($header, $rec);
            if (!empty($fullRecord['Ethnic Group']) || !empty($fullRecord['Religion'])) {

                var_dump($fullRecord['Ethnic Group']);
                var_dump($fullRecord['Religion']);

                $I->fail("A value was found. Record: " . json_encode($fullRecord));
            }
    }

    private function cleanup(AcceptanceTester $I)
    {
        $I->deleteFile($I->getDownloadDir() . self::$complianceListName . '.csv');
    }

    // #[Skip()]
    #[Group('ccpa')]
    #[Before('login')]
    #[After('logout')]
    public function createListGenCompliance(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::$complianceListName,
            self::getProjectName(),
            ProjectType::NewProject,
            [
                # its fast without email/phone append; Much slower with
                new ListGenContact(ContactType::Address),
                // new ListGenContact(ContactType::Phone, null, 'When Available'),
                // new ListGenContact(ContactType::Email),
            ],
            null,
            new ListGenGeoFilters(null, null, null, [
                // (Each of these zip codes contains a population of ~10k people)
                '81632', // CO
                '06078', // CT
                '19952', // DE
                '56470', // MN // new
                '59404', // MT
                '69341', // NE // new
                '03077', // NH
                '07876', // NJ
                '97386', // OR
                '37764', // TN // new
                '76645', // TX
                '22443', // VA
            ]),
            null,
            new ListGenAttrConfig(false, [DemographicType::Demographic])
        );

        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }


    #[Group('ccpa')]
    #[Before('login')]
    #[After('logout')]
    #[Depends('createListGenCompliance')]
    public function exportListGenComplianceList(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::$complianceListName . '.csv'
        );

        $I->waitForElementAndClick("//button[text()='Export as CSV']");
        $I->waitForElementAndClick("//button[text()='I agree']");
        $I->wait(120);
    }


    #[Group('ccpa')]
    #[Depends('exportListGenComplianceList')]
    // #[After('cleanup')]
    public function verifyListGen(AcceptanceTester $I)
    {
        $listName = self::$complianceListName . '.csv';

        $fp = fopen($I->getDownloadDir() . $listName, 'r');
        $header = fgetcsv($fp);

        while (($rec = fgetcsv($fp)) !== false) {
            self::checkComplianceErrors($I, $header, $rec);
        }

        fclose($fp);
    }
}