<?php

class DatabaseIntegrityCest
{

    // TODO: Remove/Update. What is this test suppose to do?

    // Assertions: 223
    public function countFillRate(KittiverseTester $I)
    {
        $originColumns = $I->getKVColumnsCount();
        $KVColumns = $I->getKVBuildColumnsCount();


        foreach ($originColumns as $column => $originCount) {
            $queryFormatString =  $I->getNotEmptyQueryForKey($column);
            $count = $I->executeQueryCount($queryFormatString, $column);
            $I->assertInExpectedRange($count, $originCount, 1);


            if (array_key_exists($column,$KVColumns)) {
                $I->assertEquals($count, $KVColumns[$column], "Field: ". $column);
            }

        }

    }
}
