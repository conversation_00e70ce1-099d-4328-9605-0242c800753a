<?php

namespace Tests\Kittiverse;

use Tests\Support\KittiverseTester;

ini_set('memory_limit', '20000M');

# can swap out DB table here (KittiversePINDIVRnd for example)
const DB_TABLE = 'KittiversePINDIV';

class SanityCest
{
    // TODO: Most of these KV tests are borked

    // Tests that still work:
    // Total Rec Count
    // Count of companies where SalesValue = 0
    // Count of companies where NumEmployees = 0

    // Test Ideas:
    // 1. Get the number of columns in the table (just to verify against the GFS card)
    // 2.

    public function totalRecordCount(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'totalRecordCount';

        $queryString = "SELECT COUNT(*) AS $field FROM $table";

        $count = $I->executeQueryCount($queryString, $field);
    }

    public function uniqueDomainKeyCount(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'uniqueDkCount';

        $queryString = "SELECT COUNT(DISTINCT dk) AS $field FROM $table";

        $count = $I->executeQueryCount($queryString, $field);
    }

    public function uniqueCorpDomainCount(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'uniqueCorpDomainCount';

        $query = "SELECT COUNT(DISTINCT CorpDomain) AS $field FROM $table";

        $count = $I->executeQueryCount($query, $field);
    }

    public function uniqueCorpNameCount(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'uniqueCorpNameCount';

        $query = "SELECT COUNT(DISTINCT CorpName) AS $field FROM $table";

        $count = $I->executeQueryCount($query, $field);
    }

    public function uniqueEmailCount(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'uniqueEmailCount';

        $query = "SELECT COUNT(DISTINCT EmailAddr) AS $field FROM $table";

        $count = $I->executeQueryCount($query, $field);
    }

    public function uniqueSICCount(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'uniqueSICCount';

        $query = "SELECT COUNT(DISTINCT SIC) as $field from $table";

        $count = $I->executeQueryCount($query, $field);
    }

    public function firstNameLastNameMatch(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'firstNameLastNameMatch';

        $query = "SELECT COUNT(*) AS $field
        FROM $table
        WHERE LEFT(FirstName, 4) = LEFT(LastName, 4)";

        $count = $I->executeQueryCount($query, $field);
    }

    public function emailsWithBadChars(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'BadCharsInEmailCount';

        $query = "SELECT COUNT(*) AS $field
        FROM $table
        WHERE EmailAddr LIKE '%?%'";

        $count = $I->executeQueryCount($query, $field);
    }

    public function domainsWithoutSalesCount(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'domainsWithoutSalesCount';

        $query = "SELECT COUNT(DISTINCT CorpDomain) AS $field FROM $table WHERE SalesVolume = 0";

        $count = $I->executeQueryCount($query, $field);
    }

    public function domainsWithoutNumEmployeesCount(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'domainsWithoutNumEmployeesCount';

        $query = "SELECT COUNT(DISTINCT CorpDomain) AS $field
        FROM $table
        WHERE NumEmployees = '' OR NumEmployees IS NULL";

        $count = $I->executeQueryCount($query, $field);
    }

    public function countOfDomainsGreaterThan10k(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $count = 'countOfDomainsGreaterThan10k';

        $query = "SELECT CorpDomain, COUNT(CorpDomain) AS $count
        FROM $table
        GROUP BY CorpDomain
        HAVING $count > '9999'
        ORDER BY $count DESC";

        $result = $I->executeQuery($query);
        $numberOfRows = $result->num_rows;
        echo "\nCount of domains > 10k: $numberOfRows \n";
    }

    public function countOfCorpNamesGreaterThan10k(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $count = 'countOfCorpNamesGreaterThan10k';

        $query = "SELECT CorpName, COUNT(CorpName) AS $count
        FROM $table
        GROUP BY CorpName
        HAVING $count > '9999'
        ORDER BY $count DESC";

        $result = $I->executeQuery($query);
        $numberOfRows = $result->num_rows;
        echo "\nCount of CorpName > 10k: $numberOfRows \n";
    }

   # Generates full list of domains in _data
   public function domainsOver500Rec(KittiverseTester $I)
   {
        $table = DB_TABLE;

        $query = "SELECT CorpDomain, COUNT(CorpDomain) AS count
        FROM $table
        GROUP BY CorpDomain
        HAVING count > '499'
        ORDER BY count DESC";

        $results = $I->executeQueryGetAssocArrayValueCount($query);
        $totalRows = count($results);
        echo "\nDomains Over 500 rec: $totalRows\n";

        # write results to file
        $fh = fopen(codecept_data_dir() . 'count_domains_with_500_records.csv', 'w');
        foreach ($results as $domain => $count) {
            $row = [];
            $row['domain'] = $domain;
            $row['count'] = $count;
            fputcsv($fh, $row);
        }
        fclose($fh);
    }

    public function emailAddrGreaterThan200(KittiverseTester $I)
    {
        $table = DB_TABLE;

        $query = "SELECT EmailAddr, COUNT(EmailAddr) AS count
        FROM $table
        GROUP BY EmailAddr
        HAVING count > '199'
        ORDER BY count DESC";

        $result = $I->executeQuery($query);
        $numberOfRows = $result->num_rows;
        echo "\nCount of Emails > 200: $numberOfRows \n";
    }

    /**
     * @skip
     * times out
     */
    public function liuidRetain(KittiverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'liuidRetainCount';
        $originLiuids  = $I->getLiuidList(); // length == 779

        $query = "SELECT COUNT(*) AS $field
        FROM $table
        WHERE LIUID in (".implode(',', $originLiuids).")";

        $result = $I->executeQueryCount($query, $field);
    }

    /**
     * @skip
     * times out
     */
    public function liProfileUrlRetain(KittiverseTester $I)
    {
        $originLiurls  = $I->getLIProfileURLList(); // length == 779

        $query = "SELECT RIGHT(LIProfileURL, LOCATE('/', REVERSE(LIProfileURL)) - 1) AS liurl
        FROM KittiversePINDIV
        WHERE LIProfileURL <> ''";

        $result = $I->executeQuery($query); // length == 53,516,022 yikes!

        $urlDBResult = [];
        while ($r = $result->fetch_assoc()) {
            $urlDBResult[] = $r['liurl'];
        }
        $retainedUrls = array_intersect($originLiurls, $urlDBResult);
        $retainedUrlsCount = count($retainedUrls);
        $originLiurlsCount = count($originLiurls);

        echo "Retained $retainedUrlsCount out of $originLiurlsCount";
    }

    // Deprecated: Unacceptable performance (takes ~ 15 min)
    // public function countOfRecNumberOfEmployees(KittiverseTester $I)
    // {
    //     $query = 'SELECT
    //     COUNT(`EmailAddr`) as emcount,
    //     COUNT(Distinct(`EmailAddr`)) as emuniq,
    //     `CorpDomain`,
    //     `NumEmployees`
    //     FROM `KittiversePINDIV`
    //     GROUP BY `CorpDomain`,`NumEmployees`
    //     HAVING emcount > `NumEmployees` and emcount > "10000"
    //     ORDER BY emcount DESC';

    //     $result = $I->executeQuery($query);
    //     $numberOfRows = $result->num_rows;

    //     echo "\nCount of recs > NumEmployees: $numberOfRows \n";
    // }

    // Deprecated: Unacceptable performance (PublicOrPrivate is not indexed)
    // public function domainsWithTickerCount(KittiverseTester $I)
    // {
    //     $queryFormatString = 'select count(distinct `CorpDomain`) as %s
    //     from `KittiversePINDIV`
    //     where `TickerSymbol` <> \'\' and `PublicOrPrivate` = "Public"';
    //     $count = $I->executeQuerycount($queryFormatString, DOMAINS_WITH_TICKER_COUNT);
    //     $I->assertInExpectedRange($I->getKVDomainsWitTickerCount(), $count, 1);
    // }

    // Deprecated: Unacceptable performance, scans the whole table
    // public function recsWithTickerCount(KittiverseTester $I)
    // {
    //     $queryFormatString = 'select count(*) as %s from `KittiversePINDIV` where `PublicOrPrivate` = "Public" and `TickerSymbol` <> \'\'';

    //     $count = $I->executeQuerycount($queryFormatString, RECORDS_WITH_TICKER_COUNT);
    //     $I->assertInExpectedRange($I->getKVRecordsWitTickerCount(), $count, 1);
    // }

}
