<?php

/**
 * These API tests are deprecaded in favor of Postman tests
 */

namespace Tests\Api;

use Tests\Support\ApiTester;

class B2CDemoAppendCest
{

    public static $apiUrl = '/demographic';

    public function _before(ApiTester $I) {
        $I->setHeaders($I);
    }

    # Required param combination 1
    public function appendDemoFromPhone(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'demographic',
            'phone' => '7342839588',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 2b
    public function appendDemoFromBusinessEmail(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'demographic',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 2c
    public function appendDemoFromConsumerEmail(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'demographic',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 3
    public function appendDemoFromAddressCityStateZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'demographic',
            'address' => '5231 NE 42nd St',
            'city' => 'Seattle',
            'state' => 'WA',
            'zip' => '98015',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 4
    public function appendDemoFromFirstLastCityState($I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'demographic',
            'first' => 'Bonny',
            'last' => 'Allegro',
            'city' => 'High Point',
            'state' => 'NC',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 5
    public function appendDemoFromFirstLastZip($I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'demographic',
            'first' => 'Bonny',
            'last' => 'Allegro',
            'zip' => '27265',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function respectsAllInputParams(ApiTester $I) {
        $input_query = [
            'first' => 'Robert',
            'last' => 'Aguas',
            'address' => '23111 44th Ave W APT B',
            'city' => 'Mountlake Terrace',
            'state' => 'WA',
            'zip' => '98043',
            'email' => '<EMAIL>',
            'phone' => '4257440797',
            'country' => 'US',
        ];
        $I->sendGET(self::$apiUrl, array_merge($input_query, [
            'output[]' => 'demographic',
            'test' => rand()
        ]));
        $I->seeResponseIsOK($I);
        $res_input_query = $I->grabDataFromResponseByJsonPath('versium.input_query');
        $I->assertEquals($input_query, $res_input_query[0]);
    }

    public function validOutputTypeDemographic(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'demographic',
            'first' => 'Tristan',
            'last' => 'Bull',
            'address' => '515 E Denny Way',
            'city' => 'Seattle',
            'state' => 'washington',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResponseMatchesJsonType($I::DEMOGRAPHIC_TYPE, 'versium.results[0]');
    }

    public function validOutputTypeFinancial(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'financial',
            'first' => 'Tristan',
            'last' => 'Bull',
            'address' => '515 E Denny Way',
            'city' => 'Seattle',
            'state' => 'washington',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResponseMatchesJsonType($I::FINANCIAL_TYPE, 'versium.results[0]');
    }

    public function validOutputTypeLifestyle(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'lifestyle',
            'first' => 'Tristan',
            'last' => 'Bull',
            'address' => '515 E Denny Way',
            'city' => 'Seattle',
            'state' => 'washington',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResponseMatchesJsonType($I::LIFESTYLE_TYPE, 'versium.results[0]');
    }

    public function validOutputTypePolitcal(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'political',
            'first' => 'Tristan',
            'last' => 'Bull',
            'address' => '515 E Denny Way',
            'city' => 'Seattle',
            'state' => 'washington',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResponseMatchesJsonType($I::POLITICAL_TYPE, 'versium.results[0]');
    }

    public function respectsMaxRecs(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'demographic',
            'address' => '12021 2nd Ave NW',
            'city' => 'Seattle',
            'state' => 'washington',
            'zip' => '98177',
            'cfg_maxrecs' => '2',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $num_matches = $I->grabDataFromResponseByJsonPath('versium.num_matches');
        $I->assertEquals(2, $num_matches[0]);
    }

    public function respectsDefaultMaxRecs(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'demographic',
            'address' => '12021 2nd Ave NW',
            'city' => 'Seattle',
            'state' => 'washington',
            'zip' => '98177',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $num_matches = $I->grabDataFromResponseByJsonPath('versium.num_matches');
        $I->assertEquals(1, $num_matches[0]);
    }

    public function failsWithOnlyOptionalParams(ApiTester $I) {
        $expected_err = 'The request must contain at least one of the following sets of inputs: [["email"],["phone"],["address","city","state","zip"],["first","last","city","state"],["first","last","zip"]]';
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'demographic',
            'address' => '12021 2nd Ave NW',
            'city' => 'Seattle',
            'state' => 'washington',
            'test' => rand()
        ]);
        $I->seeResponseIsJson();
        $I->seeResponseCodeIs(400);
        $errors = $I->grabDataFromResponseByJsonPath('versium.errors[0]');
        $I->assertEquals($expected_err, $errors[0]);
    }

    //TODO:
    // showoutput
    // financial/lifestyle/political
}
