<?php

/**
 * These API tests are deprecaded in favor of Postman tests
 */

namespace Tests\Api;

use Tests\Support\ApiTester;

class B2BOnlineAudienceAppendCest {

    public static $apiUrl = '/b2bOnlineAudience';

    public function _before(ApiTester $I) {
        $I->setHeaders($I);
    }

    # Required param combination 1
    public function appendAudienceFromFirstLastEmail(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'first' => 'Ansa',
            'last' => 'Sekharan',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 2
    public function appendAudienceFromFullnameEmail(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'fullname' => '<PERSON><PERSON>',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 3
    public function appendAudienceFromBusinessFirstLastCityState(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'business' => 'The Boeing Company',
            'first' => 'Michael',
            'last' => 'Luttig',
            'city' => 'CHICAGO',
            'state' => 'IL',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 4
    public function appendAudienceFromBusinessFullnameCityState(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'business' => 'The Boeing Company',
            'fullname' => 'Michael Luttig',
            'city' => 'CHICAGO',
            'state' => 'IL',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 5
    public function appendAudienceFromDomainFirstLastCityState(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'domain' => 'intuit.com',
            'first' => 'Anil',
            'last' => 'Madan',
            'city' => 'MOUNTAIN VIEW',
            'state' => 'CA',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 6
    public function appendAudienceFromDomainFullnameCityState(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'domain' => 'intuit.com',
            'fullname' => 'Anil Madan',
            'city' => 'MOUNTAIN VIEW',
            'state' => 'CA',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function respectsAllInputParams(ApiTester $I) {
        $input_query = [
            'first' => 'Michael',
            'last' => 'Luttig',
            'domain' => 'boeing.com',
            'business' => 'The Boeing Company',
            'email' => '<EMAIL>',
            'city' => 'CHICAGO',
            'state' => 'IL',
        ];
        $I->sendGET(self::$apiUrl, array_merge($input_query, [
            'test' => rand()
        ]));
        $I->seeResponseIsOK($I);
        // TODO: use jsonPath to grab the inner array: versium.input_query.*
        $res_input_query = $I->grabDataFromResponseByJsonPath('versium.input_query');
        $I->assertEquals(array_keys($input_query), array_keys($res_input_query[0]));
    }

    # verify output type
    # showoutput
    # respects maxrecs
    # Fails without required params

}