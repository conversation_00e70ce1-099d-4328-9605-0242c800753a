<?php

/**
 * These API tests are deprecaded in favor of Postman tests
 */

namespace Tests\Api;

use Tests\Support\ApiTester;

class C2BAppendCest {

    public static $apiUrl = '/c2b';

    public function _before(ApiTester $I) {
        $I->setHeaders($I);
    }

    # Required param combination 1
    public function firstLastbusinessEmailInputs(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'first' => 'Elon',
            'last' => 'Musk',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 2
    public function firstLastConsumerEmailInputs(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'first' => 'John',
            'last' => 'Doe',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function respectsAllInputParams(ApiTester $I) {
        $input_query = [
            'first' => 'Elon',
            'last' => 'Musk',
            'email' => '<EMAIL>',
            'domain' => 'tesla.com',
        ];
        $I->sendGET(self::$apiUrl, array_merge($input_query, [
            'test' => rand()
        ]));
        $I->seeResponseIsOK($I);
        $res_input_query = $I->grabDataFromResponseByJsonPath('versium.input_query');
        $I->assertEquals($input_query, $res_input_query[0]);
    }

    # this input_query needs to return every output param
    public function verifyOutputTypes(ApiTester $I) {
        $input_query = [
            'first' => 'Joe',
            'last' => 'Augustavo',
            'email' => '<EMAIL>',
            'test' => rand()
        ];
        $I->sendGET(self::$apiUrl, $input_query);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        $I->seeResponseMatchesJsonType($I::C2B_OUTPUT_TYPE, 'versium.results[0]');
    }

    public function showoutput(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'showoutput' => 'true'
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        $results = $I->grabDataFromResponseByJsonPath('versium.results');
        $I->assertEquals($results[0], array_keys($I::C2B_OUTPUT_TYPE));
    }

    public function failsWithoutRequiredParams(ApiTester $I) {
        $expectedErr = 'The request must contain at least one of the following sets of inputs: [["first","last","email"],["li_url"]]';
        $I->sendGET(self::$apiUrl, []);
        $I->seeResponseCodeIs(400);
        $errors = $I->grabDataFromResponseByJsonPath('versium.errors');
        $I->assertEquals($expectedErr, $errors[0][0]);
    }

    // TODO: respets max recs
}