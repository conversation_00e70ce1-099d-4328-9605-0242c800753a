<?php

/**
 * These API tests are deprecaded in favor of Postman tests
 */

namespace Tests\Api;

use Tests\Support\ApiTester;

class CustomCest {

    public static $apiUrl = 'custom/';

    public function _before(ApiTester $I) {
        $I->setHeaders($I);
    }

    public function customApiAutoFinanceScore(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'auto_finance_score',
            'first' => 'Elon',
            'last' => 'Musk',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function customApiConsusBestMatch(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'consus_best_match',
            'first' => 'Elon',
            'last' => 'Musk',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function customApiEmailDecryption(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'email_decryption',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function customApiTravelScore(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'travel_score',
            'first' => 'Elon',
            'last' => 'Musk',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function customApiDonorScore(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'donor_score',
            'first' => 'Elon',
            'last' => 'Musk',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function customApiGreenScore(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'green_score',
            'first' => 'Elon',
            'last' => 'Musk',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function customApiWealthScore(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'wealth_score',
            'first' => 'Elon',
            'last' => 'Musk',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function customApiDiyScore(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'diy_score',
            'first' => 'Elon',
            'last' => 'Musk',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function customApiNewTechAdopterScore(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'new_tech_adopter_score',
            'first' => 'Elon',
            'last' => 'Musk',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function customApiOnlineShopperScore(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'online_shopper_score',
            'first' => 'Elon',
            'last' => 'Musk',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

}