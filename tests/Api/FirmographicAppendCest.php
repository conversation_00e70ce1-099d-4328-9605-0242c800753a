<?php

/**
 * These API tests are deprecaded in favor of Postman tests
 */

namespace Tests\Api;

use Tests\Support\ApiTester;

class FirmographicAppendCest {

    public static $apiUrl = '/firmographic';

    public function _before(ApiTester $I) {
        $I->setHeaders($I);
    }

    // TODO: find an email input that returns results
    // This does not seem to work...
    # Required param combination 1
    /**
     * @skip
     */
    public function emailInput(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'email' => '',
            'test' => rand()
        ]);
    }

    # Required param combination 2
    public function domainInput(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'domain' => 'activisionblizzard.com',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 3
    /**
     * @skip
     * need inputs that return a result
     */
    public function businessNameFullAddressInput(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'business' => 'Chipotle Mexican Grill, Inc.',
            'address' => '1401 Wynkoop St. ',
            'city' => 'Denver',
            'state' => 'CO',
            'zip' => '80202',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 4
    public function businessNamePhoneInput(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'business' => 'Advanced Micro Devices Inc',
            'phone' => '5126021000',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function respectsAllParams(ApiTester $I) {
        $input_query = [
            'email' => '<EMAIL>',
            'domain' => 'alaskaair.com',
            'business' => 'Alaska Airlines, Inc',
            'address' => '19300 International Blvd',
            'city' => 'Seatac',
            'state' => 'WA',
            'zip' => '98188',
            'phone' => '2063925040'
        ];
        $I->sendGET(self::$apiUrl, array_merge($input_query,[
            'test' => rand()
        ]));
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        $res_input_query = $I->grabDataFromResponseByJsonPath('versium.input_query');
        $I->assertEquals($input_query, $res_input_query[0]);
    }

    # this input_query needs to return every output param
    # (however, "SIC Description" is not a column in kittiverse, it will never return this...)
    public function verifyOutputTypes(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'domain' => 'alaskaair.com',
            'business' => 'Alaska Airlines, Inc',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        $I->seeResponseMatchesJsonType($I::FIRMOGRAPHIC_OUTPUT_TYPE, 'versium.results[0]');
    }

    public function showoutput(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'showoutput' => 'true'
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        $results = $I->grabDataFromResponseByJsonPath('versium.results');
        $I->assertEquals($results[0], array_keys($I::FIRMOGRAPHIC_OUTPUT_TYPE));
    }

    public function failsWithoutRequiredParams(ApiTester $I) {
        $expectedErr = 'The request must contain at least one of the following sets of inputs: [["email"],["domain"],["business","address","city","state"],["business","phone"],["li_url"]]';
        $I->sendGET(self::$apiUrl, []);
        $I->seeResponseCodeIs(400);
        $errors = $I->grabDataFromResponseByJsonPath('versium.errors');
        $I->assertEquals($expectedErr, $errors[0][0]);
    }

    // TODO: might be hard to find a query that returns > 1 rec
    // public function respectsMaxRecs(ApiTester $I) {
    // }
}