<?php

/**
 * These API tests are deprecaded in favor of Postman tests
 */

namespace Tests\Api;

use Tests\Support\ApiTester;

class IP2DomainCest {
    public static $apiUrl = '/iptodomain';

    public function _before(ApiTester $I) {
        $I->setHeaders($I);
    }

    # Required param combination #1
    public function appendDomainFromIP(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'ip' => '***********'
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # showoutput
    public function showoutput(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'showoutput' => 'true'
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        $results = $I->grabDataFromResponseByJsonPath('versium.results');
        $I->assertEquals($results[0], array_keys($I::IP2DOMAIN_OUTPUT_TYPE));
    }

    public function failsWithoutRequiredParams(ApiTester $I) {
        $expectedErr = 'The request must contain at least one of the following sets of inputs: [["ip"]]';
        $I->sendGET(self::$apiUrl, []);
        $I->seeResponseCodeIs(400);
        $error = $I->grabDataFromResponseByJsonPath('versium.errors');
        $I->assertEquals($expectedErr, $error[0][0]);
    }
}