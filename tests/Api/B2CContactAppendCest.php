<?php

/**
 * These API tests are deprecaded in favor of Postman tests
 */

namespace Tests\Api;

use Tests\Support\ApiTester;

class B2CContactAppendCest {

    public static $apiUrl = '/contact';

    public function _before(ApiTester $I) {
        $I->setHeaders($I);
    }

    # Address: Required param combination 1
    public function appendAddressFromEmail(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'address',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Address: Required param combination 2
    public function appendAddressFromPhone(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'address',
            'phone' => '2067234189',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Address: Required param combination 3
    public function appendAddressFromFirstLastCityState(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'address',
            'first' => 'George',
            'last' => 'Oconnell',
            'city' => 'Dedham',
            'state' => 'MA',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Address: Required param combination 4
    public function appendAddressFromFirstLastZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'address',
            'first' => 'Ingrid',
            'last' => 'Aguilera',
            'zip' => '11372',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Phone: Required param combination 1
    public function appendPhoneFromEmail(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Phone: Required param combination 2
    public function appendPhoneFromAddressCityStateZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone',
            'address' => '94 JERSEY ST',
            'city' => 'DEDHAM',
            'state' => 'MA',
            'zip' => '02026',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Phone: Required param combination 3
    public function appendPhoneFromFirstLastCityState(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone',
            'first' => 'George',
            'last' => 'Oconnell',
            'city' => 'DEDHAM',
            'state' => 'MA',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Phone: Required param combination 4
    public function appendPhoneFromFirstLastZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone',
            'first' => 'Ingrid',
            'last' => 'Aguilera',
            'zip' => '11372',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }


    # Phone_mobile: Required param combination 1
    public function appendPhoneMobileFromEmail(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone_mobile',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Phone_mobile: Required param combination 2
    public function appendPhoneMobileFromAddressCityStateZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone_mobile',
            'address' => '130 Old Mill Way',
            'city' => 'Senoia',
            'state' => 'GA',
            'zip' => '30276',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Phone_mobile: Required param combination 3
    public function appendPhoneMobileFromFirstLastCityState(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone_mobile',
            'first' => 'Ashlee',
            'last' => 'Bingel',
            'city' => 'Senoia',
            'state' => 'GA',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Phone_mobile: Required param combination 4
    public function appendPhoneMobileFromFirstLastZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone_mobile',
            'first' => 'Ashlee',
            'last' => 'Bingel',
            'zip' => '30276',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }


    # Phone_multiple: Required param combination 1
    public function appendPhoneMultiFromEmail(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone_multiple',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Phone_multiple: Required param combination 2
    public function appendPhoneMultiFromAddressCityStateZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone_multiple',
            'address' => '3309 37th Ave W',
            'city' => 'Seattle',
            'state' => 'washington',
            'zip' => '98199',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Phone_multiple: Required param combination 3
    public function appendPhoneMultiFromFirstLastCityState(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone_multiple',
            'first' => 'Nichol',
            'last' => 'Barbes',
            'city' => 'Bel Air',
            'state' => 'MD',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Phone_multiple: Required param combination 4
    public function appendPhoneMultiFromFirstLastZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone_multiple',
            'first' => 'Nichol',
            'last' => 'Barbes',
            'zip' => '21015',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Email: Required param combination 1
    public function appendEmailFromPhone(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'email',
            'phone' => '2065966231',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }


    # Email: Required param combination 2
    public function appendEmailFromAddressCityStateZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'email',
            'address' => '8111 Northern Boulevard',
            'city' => 'Jackson Heights',
            'state' => 'NY',
            'zip' => '11372',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Email: Required param combination 3
    public function appendEmailFromFirstLastCityState(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'email',
            'first' => 'Ingrid',
            'last' => 'Aguilera',
            'city' => 'Jackson Heights',
            'state' => 'NY',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Email: Required param combination 4
    public function appendEmailFromFirstLastZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'email',
            'first' => 'Kate',
            'last' => 'Conneen',
            'zip' => '19468',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    public function respectsMaxRecs(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'email',
            'output[]' => 'phone',
            'output[]' => 'address',
            'first' => 'GEORGE',
            'last' => 'OCONNELL',
            'address' => '94 JERSEY ST',
            'city' => 'DEDHAM',
            'state' => 'MA',
            'zip' => '02026',
            'cfg_maxrecs' => '3',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        //TODO: again, jsonPath
        $results = $I->grabDataFromResponseByJsonPath('versium.results');
        $I->assertEquals(3, count($results[0]));
    }

    # Validate output types

    public function validOutputTypeAddress(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'address',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        $I->seeResponseMatchesJsonType($I::B2C_CONTACT_APPEND_ADDRESS_RETURN_TYPE, 'versium.results[0]');
    }

    public function validOutputTypePhone(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        $I->seeResponseMatchesJsonType($I::B2C_CONTACT_APPEND_PHONE_BEST_RETURN_TYPE, 'versium.results[0]');
    }

    public function validOutputTypePhoneMobile(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone_mobile',
            'first' => 'Ashlee',
            'last' => 'Bingel',
            'city' => 'Senoia',
            'state' => 'GA',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        $I->seeResponseMatchesJsonType($I::B2C_CONTACT_APPEND_PHONE_MOBILE_RETURN_TYPE, 'versium.results[0]');
    }

    public function validOutputTypePhoneMultiple(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'phone_multiple',
            'first' => 'Ingrid',
            'last' => 'Aguilera',
            'zip' => '11372',
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        $I->seeResponseMatchesJsonType($I::B2C_CONTACT_APPEND_PHONE_MULTIPLE_RETURN_TYPE, 'versium.results[0]');
    }

    public function validOutputTypeEmail(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'output[]' => 'email',
            'phone' => '2065966231',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
        $I->seeResponseMatchesJsonType($I::B2C_CONTACT_APPEND_EMAIL_OUTPUT_RETURN_TYPE, 'versium.results[0]');
    }

    # respects match_type = hhlv
    # respects match_type = indiv
    # showoutput
    # Fails without required params
}