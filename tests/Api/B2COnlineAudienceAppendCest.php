<?php

/**
 * These API tests are deprecaded in favor of Postman tests
 */

namespace Tests\Api;

use Tests\Support\ApiTester;

class B2COnlineAudienceAppendCest {

    public static $apiUrl = '/b2cOnlineAudience';

    public function _before(ApiTester $I) {
        $I->setHeaders($I);
    }

    # Required param combination 1
    public function appendAudienceFromEmail(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'email' => '<EMAIL>',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 2
    public function appendAudienceFromPhone(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'phone' => '4258769197',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 3
    public function appendAudienceFromAddressCityStateZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'address' => '3944 Brandywine St.',
            'city' => 'High Point',
            'state' => 'NC',
            'zip' => '27265',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 4
    public function appendAudienceFromFirstLastCityState(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'first' => 'Sandra',
            'last' => 'Finley',
            'city' => 'High Point',
            'state' => 'NC',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Required param combination 5
    public function appendAudienceFromFirstLastZip(ApiTester $I) {
        $I->sendGET(self::$apiUrl, [
            'first' => 'Christine',
            'last' => 'Mandeville',
            'zip' => '21771',
            'test' => rand()
        ]);
        $I->seeResponseIsOK($I);
        $I->seeResultsIsNotEmpty($I);
    }

    # Input query is echoed back
    public function respectsAllInputParams(ApiTester $I) {
        $input_query = [
            'first' => 'Sandra',
            'last' => 'Finley',
            'address' => '1705 Dover Dr',
            'city' => 'High Point',
            'state' => 'NC',
            'zip' => '53597',
            'email' => '<EMAIL>',
            'phone' => '6088497956',
            'country' => 'US',
        ];
        $I->sendGET(self::$apiUrl, array_merge($input_query, [
            'test' => rand()
        ]));
        $I->seeResponseIsOK($I);
        $res_input_query = $I->grabDataFromResponseByJsonPath('versium.input_query');
        $I->assertEquals($input_query, $res_input_query[0]);
    }


}