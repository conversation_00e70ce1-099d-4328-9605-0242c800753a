modules:
  config:
    WebDriver:
      browser: 'chrome'
      capabilities:
        chromeOptions:
          # Full list of Chromium switches: https://peter.sh/experiments/chromium-command-line-switches/
          # args: ["--start-maximized","--disable-popup-blocking","--incognito","--ignore-certificate-errors"]
          prefs:
            # Automatically accepts browser-level notifications
            'profile.managed_default_content_settings.notifications': 1
            # set file download dir
            'download.default_directory': '/Users/<USER>/selenium_downloads'