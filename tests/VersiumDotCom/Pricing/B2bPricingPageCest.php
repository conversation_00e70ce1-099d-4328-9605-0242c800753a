<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Pricing\B2bPricingPage;

class B2bPricingPageCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/b2b-pricing');
    }

    /**
     * @group sanity
     */
    public function verifyLinks(VersiumDotComTester $I) {
        foreach(B2bPricingPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }

    /**
     * @group full
     */
    public function clickThroughLinks(VersiumDotComTester $I) {
        $UrlMap = [
            B2bPricingPage::$startFreeTrialLink1 => '/b2b-signup',
            B2bPricingPage::$choosePricingLink1 => '/b2b-plan-request',
            B2bPricingPage::$choosePricingLink2 => '/b2b-plan-request',
            B2bPricingPage::$choosePricingLink3 => '/b2b-plan-request',
            B2bPricingPage::$contactSalesLink1 => '/b2b-plan-request',
            B2bPricingPage::$contactSalesLink2 => '/b2b-plan-request',
            B2bPricingPage::$contactSalesLink3 => '/b2b-plan-request',
            B2bPricingPage::$contactUsLink => '/contact-form',
            B2bPricingPage::$getStartedNowLink => '/b2b-signup'
        ];
        $I->acceptCookies($I);
        foreach($UrlMap as $link => $url) {
            $I->waitForElementAndClick($link);
            $I->canSeeCurrentUrlEquals($url);
            $I->amOnPage('/b2b-pricing');
        }
    }

}