<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Pricing\B2cPricingPage;

class B2cPricingPageCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/b2c-pricing');
    }

    /**
     * @group sanity
     */
    public function verifyLinks(VersiumDotComTester $I) {
        foreach(B2cPricingPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }

    /**
     * @group full
     */
    public function clickThroughLinks(VersiumDotComTester $I) {
        $urlMap = [
            B2cPricingPage::$startFreeTrialLink1 => '/b2c-signup',
            B2cPricingPage::$choosePricingLink1 => '/b2c-plan-request',
            B2cPricingPage::$choosePricingLink2 => '/b2c-plan-request',
            B2cPricingPage::$choosePricingLink3 => '/b2c-plan-request',
            B2cPricingPage::$contactSalesLink1 => '/b2c-plan-request',
            B2cPricingPage::$contactSalesLink2 => '/b2c-plan-request',
            B2cPricingPage::$contactSalesLink3 => '/b2c-plan-request',
            B2cPricingPage::$contactSalesLink4 => '/b2c-plan-request',
            B2cPricingPage::$contactUsLink => '/contact-form',
            B2cPricingPage::$getStartedNowLink => '/b2c-signup'
        ];
        $I->acceptCookies($I);
        foreach ($urlMap as $link => $url) {
            $I->waitForElementAndClick($link);
            $I->canSeeCurrentUrlEquals($url);
            $I->amOnPage('/b2c-pricing');
        }
    }
}