<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Misc\B2bFirstPartyPricingPage;

class B2bFirstPartyPricingCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/b2b-pricing-1st-party');
    }

    public function verifyLinks(VersiumDotComTester $I) {
        foreach(B2bFirstPartyPricingPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }
}