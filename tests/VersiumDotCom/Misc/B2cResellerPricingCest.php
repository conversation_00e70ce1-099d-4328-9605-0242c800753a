<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Misc\B2cResellerPricingPage;

class B2cResellerPricingCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/b2c-reseller-pricing');
    }

    public function verifyLinks(VersiumDotComTester $I) {
        foreach(B2cResellerPricingPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }
}