<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Misc\B2cFirstPartyPricingPage;

class B2cFirstPartyPricingCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/b2c-1st-party-pricing');
    }

    public function verifyLinks(VersiumDotComTester $I) {
        foreach(B2cFirstPartyPricingPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }
}