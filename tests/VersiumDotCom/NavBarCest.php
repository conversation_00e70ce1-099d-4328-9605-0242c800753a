<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\NavBar;

class NavBarCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/');
    }

    /**
     * @group full
     * @group sanity
     */
    public function clickThroughTopNavBarLinks(VersiumDotComTester $I) {
        $urlMap = [
            NavBar::$whyVersiumLink => '/why-versium',
            NavBar::$logInLink => '/login',
            NavBar::$tryItFreeLink => '/choose-trial'
        ];
        foreach($urlMap as $elem => $url) {
            $I->waitForElementAndClick($elem);
            $I->seeCurrentUrlEquals($url);
            $I->moveBack();
        }

        $I->waitForElement(NavBar::$versiumHomeLogo);
        $I->seeCurrentUrlEquals('/');
    }

    /**
     * @group full
     * @group sanity
     */
    public function clickThroughProductNavLinks(VersiumDotComTester $I) {
        $urlMap = [
            NavBar::$reachB2bLink => '/reach-b2b',
            NavBar::$reachB2cLink => '/reach-b2c',
            NavBar::$versiumPredictLink => '/predict'
        ];
        foreach($urlMap as $link => $url) {
            $I->waitForElementAndClick(NavBar::$productLink);
            $I->waitForElementAndClick($link);
            $I->canSeeCurrentUrlEquals($url);
        }

        $I->waitForElementAndClick(NavBar::$productLink);
        $I->waitForElementAndClick(NavBar::$reachAPIsLink);
        $url = $I->grabFullUrl();
        $I->assertMatchesRegularExpression('/api-documentation.versium.com/', $url);
    }

    /**
     * @group full
     * @group sanity
     */
    public function clickThroughPricingNavLinks(VersiumDotComTester $I) {
        $urlMap = [
            NavBar::$pricingB2bLink => '/b2b-pricing',
            NavBar::$pricingB2cLink => '/b2c-pricing'
        ];
        foreach($urlMap as $link => $url) {
            $I->waitForElementAndClick(NavBar::$pricingLink);
            $I->waitForElementAndClick($link);
            $I->seeCurrentUrlEquals($url);
        }
    }

    /**
     * @group full
     * @group sanity
     */
    public function clickThroughResourcesNavLinks(VersiumDotComTester $I) {
        $urlMap = [
            NavBar::$aboutVersiumLink => '/about-versium',
            NavBar::$versiumBlogLink => '/blogs',
            NavBar::$caseStudiesLink => '/case-studies',
            NavBar::$webinarsLink => '/webinars',
            NavBar::$newsRoomLink => '/newsroom',
            NavBar::$techGlossaryLink => '/tech-glossary'
        ];
        foreach($urlMap as $link => $url) {
            $I->waitForElementAndClick(NavBar::$resourcesLink);
            $I->waitForElementAndClick($link);
            $I->seeCurrentUrlEquals($url);
        }
    }

    /**
     * @group full
     * @group sanity
     */
    public function clickThroughSolutionsNavLinks(VersiumDotComTester $I) {
        $urlMap = [
            NavBar::$paidAdvertisingLink => '/solutions/b2b-paid-advertising',
            NavBar::$acctBasedMarketingLink => '/solutions/account-based-marketing',
            NavBar::$b2bAgenciesLink => '/solutions/agencies',
            NavBar::$hubspotLink => '/hubspot',
            NavBar::$partnersLink => '/partner'
        ];
        foreach($urlMap as $link => $url) {
            $I->waitForElementAndClick(NavBar::$solutionsLink);
            $I->waitForElementAndClick($link);
            $I->seeCurrentUrlEquals($url);
        }
    }
}