<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Product\ReachForB2BPage;

class ReachForB2BPageCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/reach-b2b');
    }

    /**
     * @group sanity
     */
    public function verifyLinks(VersiumDotComTester $I) {
        foreach(ReachForB2BPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }

    /**
     * @group full
     */
    public function clickThroughLinks(VersiumDotComTester $I) {
        $urlMap = [
            ReachForB2BPage::$startFreeTrialLink1 => '/choose-trial',
            ReachForB2BPage::$startFreeTrialLink2 => '/choose-trial?_gl=1*ddm58e*_gcl_aw*R0NMLjE2MjYyODg2MTkuQ2p3S0NBandscnFIQmhCeUVpd0FuTG1ZVU5FMW0yTUtBOFJJZWNOMUZCc2pONlFJM0FHMGZqLU1pRzZiQUZ6VUQtNkZnUG1kQlUwb19Cb0NETThRQXZEX0J3RQ..',
            ReachForB2BPage::$viewAllCaseStudiesLink => '/case-studies'
        ];
        $I->acceptCookies($I);
        foreach($urlMap as $link => $url) {
            $I->waitForElementAndClick($link);
            $I->Pause();
            $I->canSeeCurrentUrlEquals($url);
            $I->amOnPage('/reach-b2b');
        }
    }
}