<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Product\PredictPage;

class PredictPageCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/predict');
    }

    /**
     * @group sanity
     */
    public function verifyLinks(VersiumDotComTester $I) {
        foreach(PredictPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }

    /**
     * @group full
     */
    public function clickThroughLinks(VersiumDotComTester $I) {
        $urlMap = [
            PredictPage::$learnMoreLink1 => '/en-us/product/dynamics-365/versium-analytics.e74ab7ae-bf54-4561-9e76-373c1a66016d',
            PredictPage::$blogPostLink => '/blog/meet-your-new-best-friend-the-versium-identity-graph',
            PredictPage::$learnMoreLink2 => '/en-us/product/dynamics-365/versium-analytics.e74ab7ae-bf54-4561-9e76-373c1a66016d'
        ];
        $I->acceptCookies($I);
        foreach($urlMap as $link => $url) {
            $I->waitForElementAndClick($link);
            $I->canSeeCurrentUrlEquals($url);
            $I->amOnPage('/versium-predict');
        }
    }
}