<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Product\ReachForB2CPage;

class ReachForB2CPageCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/reach-b2c');
        $I->acceptCookies($I);
    }

    /**
     * @group sanity
     */
    public function verifyLinks(VersiumDotComTester $I) {
        foreach(ReachForB2CPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }

        $I->click(ReachForB2CPage::$consumerContactAppendTab);
        $I->wait(1);
        $I->canSeeElement(ReachForB2CPage::$learnMoreLink2);
    }

    /**
     * @group full
     */
    public function clickThroughLinks(VersiumDotComTester $I) {
        $urlMap = [

        ];
    }
}