<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Solutions\B2bAgenciesPage;

class B2bAgenciesPageCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/solutions/agencies');
    }

    /**
     * @group sanity
     */
    public function verifyLinks(VersiumDotComTester $I) {
        foreach(B2bAgenciesPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }

}
