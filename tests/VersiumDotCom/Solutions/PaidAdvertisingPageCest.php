<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Solutions\PaidAdvertisingPage;

class PaidAdvertisingPageCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/solutions/b2b-paid-advertising');
    }

    /**
     * @group sanity
     */
    public function verifyLinks(VersiumDotComTester $I) {
        foreach(PaidAdvertisingPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }

}