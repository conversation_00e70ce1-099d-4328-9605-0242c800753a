<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Solutions\AccountBasedMarketingPage;

class AccountBasedMarketingPageCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/solutions/account-based-marketing');
    }

    /**
     * @group sanity
     */
    public function veryLinks(VersiumDotComTester $I) {
        foreach(AccountBasedMarketingPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }

}