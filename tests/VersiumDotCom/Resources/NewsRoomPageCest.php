<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Resources\NewsRoomPage;

class NewsRoomPageCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/newsroom');
    }

    /**
     * @group sanity
     */
    public function verifyLinks(VersiumDotComTester $I) {
        foreach(NewsRoomPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }

}