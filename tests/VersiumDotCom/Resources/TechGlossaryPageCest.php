<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\Resources\TechGlossaryPage;

class TechGlossaryPageCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/tech-glossary');
    }

    /**
     * @group sanity
     */
    public function verifyLinks(VersiumDotComTester $I) {
        foreach(TechGlossaryPage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }

}