<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\HomePage;

class HomePageCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/');
    }

    /**
     * @group full
     * @group sanity
     */
    public function verifyChatWidget(VersiumDotComTester $I) {
        $I->acceptCookies($I);
        # Expand chat
        $I->waitForElementAndClick(HomePage::$chatWidgetiFrame);
        $I->switchToIFrame('#insent-iframe');
        $I->waitForElement(HomePage::$chatWidgetGreeting);
        # Close chat
        $I->click(HomePage::$chatWidgetClosebtn);
        $I->dontSeeElement(HomePage::$chatWidgetGreeting);
    }

    /**
     * @group full
     * @group sanity
     */
    public function verifySEOTags(VersiumDotComTester $I) {
        foreach(HomePage::getSEOTags() as $tag) {
            $I->canSeeElementInDOM($tag);
        }
    }

    /**
     * @group sanity
     */
    public function verifyLinks(VersiumDotComTester $I) {
        foreach(HomePage::getLinks() as $link) {
            $I->canSeeElement($link);
        }
    }

    /**
     * @group full
     */
    public function clickThroughHomePageLinks(VersiumDotComTester $I) {
        $urlMap = [
            HomePage::$learnMoreLink1 => '/why-versium',
            HomePage::$learnMoreLink2 => '/reach-b2b',
            HomePage::$startFreeTrialLink1 => '/choose-trial',
            HomePage::$learnMoreLink3 => '/blog/omni-channel-marketing-and-multi-channel-marketing-explained',
            HomePage::$learnMoreLink4 => '/solutions/b2b-paid-advertising',
            HomePage::$learnMoreLink5 => '/blog/omni-channel-marketing-and-multi-channel-marketing-explained',
            HomePage::$learnMoreLink6 => '/solutions/account-based-marketing',
            HomePage::$viewCaseStudiesLink => '/case-studies',
            HomePage::$startFreeTrialLink2 => '/choose-trial',
        ];
        $I->acceptCookies($I);
        foreach($urlMap as $link => $url) {
            $I->waitForElementAndClick($link);
            $I->canSeeCurrentUrlEquals($url);
            $I->amOnPage('/');
        }
    }
}