<?php

use Tests\Support\VersiumDotComTester;
use Tests\Support\Page\Versium\HomePage;
use Tests\Support\Page\Versium\Footer;

class FooterCest {

    public function _before(VersiumDotComTester $I) {
        $I->amOnPage('/');
    }

    /**
     * @group full
     * @group sanity
     */
    public function clickThroughFooterAboutLinks(VersiumDotComTester $I) {
        $urlMap = [
            Footer::$aboutUsFooterLink => '/about-versium',
            Footer::$whyVersiumFooterLink => '/why-versium',
            Footer::$ourProductsFooterLink => '/about-versium#our-products',
            Footer::$careersFooterLink => '/careers'
        ];
        foreach($urlMap as $link => $url) {
            $I->scrollTo(HomePage::$footerElem);
            $I->waitForElementAndClick($link);
            $I->canSeeCurrentUrlEquals($url);
        }
    }

    /**
     * @group full
     * @group sanity
     */
    public function clickThroughFooterReachLinks(VersiumDotComTester $I) {
        $urlMap = [
            Footer::$reachForB2bFooterLink => '/reach-b2b',
            Footer::$reachForB2cFooterLink => '/reach-b2c',
            Footer::$b2bPricingFooterLink => '/b2b-pricing',
            Footer::$b2cPricingFooterLink => '/b2c-pricing',
            Footer::$requestTrialFooterLink => '/choose-trial?_gl=1*1cb04xm*_gcl_aw*R0NMLjE2MjYyODg2MTkuQ2p3S0NBandscnFIQmhCeUVpd0FuTG1ZVU5FMW0yTUtBOFJJZWNOMUZCc2pONlFJM0FHMGZqLU1pRzZiQUZ6VUQtNkZnUG1kQlUwb19Cb0NETThRQXZEX0J3RQ..',
            Footer::$loginFooterLink => '/login'
        ];
        foreach($urlMap as $link => $url) {
            $I->amOnPage('/');
            $I->scrollTo(HomePage::$footerElem);
            $I->waitForElementAndClick($link);
            $I->canSeeCurrentUrlEquals($url);
        }
    }

    /**
     * @group full
     * @group sanity
     */
    public function clickThroughFooterResourcesLinks(VersiumDotComTester $I) {
        $urlMap = [
            Footer::$webinarsFooterLink => '/webinars',
            Footer::$caseStudiesFooterLink => '/case-studies',
            Footer::$techGlossayFooterLink => '/tech-glossary',
            Footer::$versiumBlogFooterLink => '/blogs',
            Footer::$newsroomFooterLink => '/newsroom'
        ];
        foreach($urlMap as $link => $url) {
            $I->scrollTo(HomePage::$footerElem);
            $I->waitForElementAndClick($link);
            $I->canSeeCurrentUrlEquals($url);
        }
    }

    /**
     * @group full
     * @group sanity
     */
    public function clickThroughFooterSupportLinks(VersiumDotComTester $I) {
        $urlMap = [
            Footer::$contactUsFooterLink => '/contact-us',
            Footer::$supportFormFooterLink => '/support-form',
            Footer::$viewFAQsFooterLink => '/contact-us#faq',
            Footer::$requestTrialSupportFooterLink => '/choose-trial?_gl=1*1r8sr82*_gcl_aw*R0NMLjE2MjYyODg2MTkuQ2p3S0NBandscnFIQmhCeUVpd0FuTG1ZVU5FMW0yTUtBOFJJZWNOMUZCc2pONlFJM0FHMGZqLU1pRzZiQUZ6VUQtNkZnUG1kQlUwb19Cb0NETThRQXZEX0J3RQ..'
        ];
        foreach($urlMap as $link => $url) {
            $I->amOnPage('/');
            $I->scrollTo(HomePage::$footerElem);
            $I->waitForElementAndClick($link);
            $I->canSeeCurrentUrlEquals($url);
        }
    }
}