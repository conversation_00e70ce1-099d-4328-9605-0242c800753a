<?php

namespace Tests\Converse;

use Tests\Support\ConverseTester;

const DB_TABLE = 'Converse';

class SanityCest
{
    public function recordCount(ConverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'RecordCount';

        $queryString = "SELECT COUNT(*) AS $field FROM $table";

        $count = $I->executeQueryCount($queryString, $field);
    }

    public function totalPhones(ConverseTester $I)
    {
        $table = DB_TABLE;
        $field = 'TotalPhones';

        $queryString = "SELECT COUNT(Phone) AS $field
        FROM $table
        WHERE Phone <> ''";

        $count = $I->executeQueryCount($queryString, $field);
    }

    public function duplicatePhones(ConverseTester $I)
    {
        $table = DB_TABLE;

        $query = "SELECT Phone, COUNT(Phone) AS count
        FROM $table
        GROUP BY Phone
        HAVING count > 199
        ORDER BY count DESC";

        $result = $I->executeQuery($query);
        $rows = $result->fetch_all();
        $resultsMap = $I->buildResultsMap($rows);

        $I->echoResults($resultsMap, $I::$duplicatePhoneNumbersToTrack);
    }

    //mem allocation issue
    public function duplicateEmails(ConverseTester $I)
    {
        $table = DB_TABLE;

        $query = "SELECT EmailAddr, COUNT(EmailAddr) AS count
        FROM $table
        GROUP BY EmailAddr
        HAVING count > 1
        ORDER BY count DESC";

        $result = $I->executeQuery($query);
        $rows = $result->fetch_all();
        $resultsMap = $I->buildResultsMap($rows);

        $I->echoResults($resultsMap, $I::$duplicateEmailsToTrack);
    }

    public function genderCodeRatio(ConverseTester $I)
    {
        $table = DB_TABLE;

        $query = "SELECT GenderCode, count(GenderCode)
        FROM $table
        WHERE GenderCode <> ''
        GROUP BY GenderCode;";

        $result = $I->executeQuery($query);
        $rows = $result->fetch_all();
        $resultsMap = $I->buildResultsMap($rows);

        print_r($resultsMap);
    }

    public function politicalParty(ConverseTester $I)
    {
        $table = DB_TABLE;

        $query = "SELECT PoliticalParty, COUNT(PoliticalParty)
        FROM $table
        WHERE PoliticalParty <> ''
        GROUP BY PoliticalParty";

        $result = $I->executeQuery($query);
        $rows = $result->fetch_all();
        $resultsMap = $I->buildResultsMap($rows);

        $I->echoResults($resultsMap, $I::$politicalPartiesToTrack);
    }

    public function education(ConverseTester $I)
    {
        $table = DB_TABLE;

        $query = "SELECT Education, COUNT(Education)
        FROM $table
        WHERE Education <> ''
        GROUP BY Education";

        $result = $I->executeQuery($query);
        $rows = $result->fetch_all();
        $resultsMap = $I->buildResultsMap($rows);

        $I->echoResults($resultsMap, $I::$educationCodesToTrack);
    }

    public function netWorth(ConverseTester $I)
    {
        $table = DB_TABLE;

        $query = "SELECT NetWorth, COUNT(NetWorth)
        FROM $table
        WHERE NetWorth <> ''
        GROUP BY NetWorth";

        $result = $I->executeQuery($query);
        $rows = $result->fetch_all();
        $resultsMap = $I->buildResultsMap($rows);

        $I->echoResults($resultsMap, $I::$netWorthCodesToTrack);
    }

    public function numberOfChildren(ConverseTester $I)
    {
        $table = DB_TABLE;

        $query = "SELECT NumberOfChildren, COUNT(NumberOfChildren)
        FROM $table
        WHERE NumberOfChildren IS NOT NULL
        GROUP BY NumberOfChildren";

        $result = $I->executeQuery($query);
        $rows = $result->fetch_all();
        $resultsMap = $I->buildResultsMap($rows);

        $I->echoResults($resultsMap, $I::$numberOfChildrenToTrack);
    }

    public function homeOwnerOrRenter(ConverseTester $I)
    {
        $table = DB_TABLE;

        $query = "SELECT HomeOwnerRenter, COUNT(HomeOwnerRenter)
        FROM $table
        WHERE HomeOwnerRenter <> ''
        GROUP BY HomeOwnerRenter";

        $result = $I->executeQuery($query);
        $rows = $result->fetch_all();
        $resultsMap = $I->buildResultsMap($rows);

        $I->echoResults($resultsMap, $I::$ownerRenterKeysToTrack);
    }
}
