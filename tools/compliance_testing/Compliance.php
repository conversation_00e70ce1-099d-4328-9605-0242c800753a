<?php


const BASE_URL_REACH_PROD = 'https://api.versium.com/v2';
const BASE_URL_REACH_STG = 'https://api-stg.versium.com/v2';
const BASE_URL_DF_PROD = 'https://api.datafinder.com/v2';
const REPORT_DIR = '/home/<USER>/tbull/compliance_testing/reports/';
// const REPORT_DIR = '/Users/<USER>/Development/reach-testing/tests/Support/Data/compliance_testing/reports/';
const INPUT_LIST_PATH = '/home/<USER>/tbull/compliance_testing/optout.csv';
// const INPUT_LIST_PATH = '/Users/<USER>/Development/reach-testing/tests/Support/Data/optout.csv';
const INPUT_FIELDS = ['first', 'last', 'address', 'city', 'state', 'zip', 'phone', 'email'];

enum VersiumService: string {
    case DF = 'DataFinder';
    case REACH = 'Reach';
}
enum Environment {
    case PROD;
    case STG;
}


/**
 * @param array $inputParams - The fields in the list to send as request params
 * @param string $operationUrl - Reach/DF endpoint to hit
 * @param VersiumService $service - Reach || DataFinder
 * @param Environment $env - Currently only supports running against production
 * @param int $testNumber - The test number to run; The API key to use according to REACH_API_KEY_MAP
 */
function runTest(
    array $inputParams,
    string $operationUrl,
    VersiumService $service,
    Environment $env,
    $testNumber,
)
{
    $counter = [
        'curl_err_count' => 0,
        'curl_errs' => [],
        'reach_err_count' => 0,
        'reach_errs' => [],
        'optout_err_count' => 0,
        'optout_errs' => [],
        'example_url' => '',
        'recs_processed' => 0,
    ];
    $urlExample = null;
    $startTime = microtime(true);

    # curl_multi
    $mh = curl_multi_init();
    $handles = [];
    $batchSize = match($service) {
        VersiumService::REACH => 7,
        VersiumService::DF    => 8,
    };

    # read in $inputList
    $fp = fopen(INPUT_LIST_PATH, 'r');
    $header = fgetcsv($fp);

    while (($rec = fgetcsv($fp)) !== false) {
        # gather params based on selected inputs
        $inputRec = array_combine($header, $rec);
        $params = getParams($inputRec, $inputParams, $service);
        $reqUrl = createReqUrl($operationUrl, $params, $service, $env);

        if ($urlExample == null) {
            $urlExample = $reqUrl;
            $counter['example_url'] = $urlExample;
        }

        # init curl
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $reqUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        if ($service === VersiumService::REACH) {
            setReachHeaders($env, $ch, $testNumber);
        };
        curl_multi_add_handle($mh, $ch);
        $handles[] = [$ch, $inputRec, $reqUrl];

        # send batch
        if (count($handles) === $batchSize) {
            $responses = sendBatchRequest($mh, $handles, $counter, $service, $env, $testNumber);

            # log progress
            $counter['recs_processed'] += count($responses);
            if ($counter['recs_processed'] % 20000 === 0) {
            // if ($counter['recs_processed'] % 100 === 0) {
                echo "Processed $counter[recs_processed] records for $service->value test #$testNumber \n";
            }

            foreach($responses as $response) {
                processResponse($response, $counter, $service);
            }
        }

    }
    fclose($fp);

    # send any remaining requests
    if (count($handles) > 0) {
        $responses = sendBatchRequest($mh, $handles, $counter, $service, $env, $testNumber);

        foreach($responses as $response) {
            processResponse($response, $counter, $service);
        }
    }

    generateReport($counter, $startTime, $operationUrl, $urlExample, $testNumber, $service);
}

function setReachHeaders(Environment $env, $handle, $testNumber) {
    if ($env === Environment::PROD) {
        curl_setopt($handle, CURLOPT_HTTPHEADER, ['X-Versium-Api-Key: ' . REACH_API_KEY_MAP[$testNumber]]);
    } else {
        # Not currently supporting staging
        // curl_setopt($handle, CURLOPT_HTTPHEADER, ['X-Versium-Api-Key: ' . API_KEY_STG]);
    }
}

function getParams(array $inputRec, array $inputParams, VersiumService $service): array
{
    $params = [];
    foreach ($inputRec as $header => $value) {
        if (in_array($header, $inputParams)) {
            match($service) {
                VersiumService::REACH => $params[$header] = $value,
                VersiumService::DF => $params['d_' . $header] = $value,
            };
        }
    }
    return $params;
}

function createReqUrl(
    string $operationUrl,
    array $params = [],
    VersiumService $service = VersiumService::REACH,
    Environment $env = Environment::PROD
    ): string
{
    $auxParams = [
        'cacheBuster' => rand(),
    ];
    $finalParams = array_merge($params, $auxParams);
    $baseUrl = match($env) {
        Environment::PROD => $service === VersiumService::REACH ? BASE_URL_REACH_PROD : BASE_URL_DF_PROD,
        // Environment::STG => $service === VersiumService::REACH ? BASE_URL_REACH_STG : BASE_URL_DF_STG,
    };

    return $baseUrl . $operationUrl . http_build_query($finalParams);
}

function sendBatchRequest($multiHandle, &$handles, &$counter, $service, $env, $testNumber): array
{
    $responses = [];

    # execute multi curl
    $running = null;
    do {
      curl_multi_exec($multiHandle, $running);
      usleep(10000);
    } while ($running);

    # process the responses
    foreach ($handles as [$ch, $inputRec, $reqUrl]) {
        $response = curl_multi_getcontent($ch);
        $errNum = curl_errno($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        # retry
        if (in_array($status, [429, 500])) {
            echo "Got status: $status. ErrNum: $errNum. Retrying...\n";
            // usleep(500000);
            usleep(50000);

            $nch = curl_init();
            curl_setopt($nch, CURLOPT_URL, $reqUrl);
            curl_setopt($nch, CURLOPT_RETURNTRANSFER, 1);
            if ($service === VersiumService::REACH) {
                setReachHeaders($env, $nch, $testNumber);
            };

            // $errNum = curl_errno($nch);
            $res = curl_exec($nch);
            $status = curl_getinfo($nch, CURLINFO_HTTP_CODE);
            echo "new status: $status. New Response: \n";
            // var_dump($res);
        }

        # curl errors
        if ($errNum || $status != 200) {
            // var_dump($status);
            $counter['curl_err_count'] += 1;
            array_push($counter['curl_errs'], 'status: ' . $status . ' url: ' . $reqUrl);
        }
        # reach errors
        $jsonRes = json_decode($response, false, 10);
        if ($service === VersiumService::REACH && isset($jsonRes->versium->errors)) {
            $counter['reach_err_count'] += 1;
            array_push($counter['reach_errs'], $jsonRes->versium->errors[0]);
        }
        if ($service === VersiumService::DF && isset($jsonRes->datafinder->errors)) {
            $counter['reach_err_count'] += 1;
            array_push($counter['reach_errs'], $jsonRes->datafinder->errors[0]);
        }

        # store the response
        $responses[] = [$jsonRes, $inputRec, $reqUrl];

        curl_multi_remove_handle($multiHandle, $ch);
    }
    $handles = [];

    return $responses;
}

function processResponse($response, array &$counter, $service)
{
    [$decodedResponse, $inputRec, $reqUrl] = $response;
    // var_dump($decodedResponse);

    if ($service === VersiumService::REACH) {
        if (!empty($decodedResponse->versium->results)) {
            $counter['optout_err_count'] += 1;
            array_push($counter['optout_errs'], $reqUrl);
        }
    } else if ($service === VersiumService::DF) {
        if (!empty($decodedResponse->datafinder->results)) {
            $counter['optout_err_count'] += 1;
            array_push($counter['optout_errs'], $reqUrl);
        }
    }
}

function generateReport(
    array $counter,
    string $startTime,
    string $operationUrl,
    string $urlExample,
    int $testNumber,
    VersiumService $service,
    ) {
    $endTime = microtime(true);
    $executionTime = $endTime - $startTime;
    $executionTimeFrmt = time2string($executionTime);
    $curlErrs = $counter['curl_err_count'];
    $reachErrs = $counter['reach_err_count'];
    $optOutErrs = $counter['optout_err_count'];
    $maxLines = 5;

    # Log to the console
    echo "/******************* Finished: $operationUrl ******************/ \n";
    echo "There were $curlErrs request errors: \n";
    for ($i = 0; $i < $maxLines; $i++) {
        if (isset($counter['curl_errs'][$i])) {
            $cErr = $counter['curl_errs'][$i];
            echo "  $cErr \n";
        }
    }
    echo "There were $reachErrs Reach errors. \n";
    for ($i = 0; $i < $maxLines; $i++) {
        if (isset($counter['reach_errs'][$i])) {
            $rErr = $counter['reach_errs'][$i];
            echo "  $rErr \n";
        }
    }
    echo "There were $optOutErrs Opt out errors. \n";
    for ($i = 0; $i < $maxLines; $i++) {
        if (isset($counter['optout_errs'][$i])) {
            $url = $counter['optout_errs'][$i];
            echo "  $url \n";
        }
    }
    echo "Script execution time: $$executionTimeFrmt \n";
    echo "Example Request Url: $urlExample \n";

    # Write to file
    $resportService = match($service) {
        VersiumService::REACH => 'Reach_',
        VersiumService::DF    => 'DF_',
    };
    $reportName = date('Y-m-d') . '_Compliance_' . $resportService . $testNumber . '.csv';
    $reportPath = REPORT_DIR . $reportName;

    $fp = fopen($reportPath, 'a');
    fputcsv($fp, ["--------------- Finished: $operationUrl ---------------"]);
    fputcsv($fp, ["Optout Errors: $optOutErrs"]);
    fputcsv($fp, ["Request Errors: $curlErrs"]);
    fputcsv($fp, ["Reach Errors: $reachErrs"]);
    fputcsv($fp, ["Script execution time: $$executionTimeFrmt."]);
    fputcsv($fp, ["Example Request Url: $urlExample. \n"]);

    fputcsv($fp, ['--------------------------']);

    fputcsv($fp, ["Optout Errors: $optOutErrs"]);
    if (count($counter['optout_errs']) > 0) {
        foreach($counter['optout_errs'] as $url) {
            fputcsv($fp, [$url]);
        }
    }

    fputcsv($fp, ['--------------------------']);

    fputcsv($fp, ["Request Errors: $curlErrs"]);
    if (count($counter['curl_errs']) > 0) {
        foreach ($counter['curl_errs'] as $status) {
            fputcsv($fp, ["  status: " . $status]);
        };
    }

    fputcsv($fp, ['--------------------------']);

    fputcsv($fp, ["Reach Errors: $reachErrs"]);
    if (count($counter['reach_errs']) > 0) {
        foreach ($counter['reach_errs'] as $err) {
            fputcsv($fp, ["  error: " . $err]);
        }
    }

    fclose($fp);
}

function time2string($time) {
    $d = floor($time/86400);
    $_d = ($d < 10 ? '0' : '').$d;

    $h = floor(($time-$d*86400)/3600);
    $_h = ($h < 10 ? '0' : '').$h;

    $m = floor(($time-($d*86400+$h*3600))/60);
    $_m = ($m < 10 ? '0' : '').$m;

    $s = $time-($d*86400+$h*3600+$m*60);
    $_s = ($s < 10 ? '0' : '').$s;

    $time_str = $_d.':'.$_h.':'.$_m.':'.$_s;

    return $time_str;
}

const REACH_TESTS = [
    1 => '/demographic?output[]=full_demographic&',
    2 => '/contact?output[]=address&match_type=indiv&',
    3 => '/contact?output[]=email&match_type=indiv&',
    4 => '/contact?output[]=phone_mobile&match_type=indiv&',
    5 => '/contact?output[]=phone_multiple&match_type=indiv&',
    6 => '/c2b?',
];
const REACH_API_KEY_MAP = [                      //API keys all belong to: <EMAIL>
    1 => '40f41efd-6d39-4e52-b84c-7a3815f135a5', //QA-API-Compliance-1
    2 => 'ed05f248-e8af-4ba7-933e-3e3190327cda', //QA-API-Compliance-2
    3 => 'eec285fc-42b3-4801-b08b-ba267661222e', //QA-API-Compliance-3
    4 => '9722440e-26df-4f42-b54c-a494dbedd5bc', //QA-API-Compliance-4
    5 => 'c2288bd6-7e39-47e7-9ea8-de878c82791d', //QA-API-Compliance-5
    6 => 'c65ce048-bd4a-4fe9-91f2-affe54306ed6', //QA-API-Compliance-6
];
const DF_TESTS = [
    1 => '/qdf.php?service=phone&k2=7fanmsuz2tujp0adnnku0efo&',     //<EMAIL>
    2 => '/qdf.php?service=email&k2=zcsd0gamb9adcqcucrfjeduj&',     //<EMAIL>
    3 => '/qdf.php?service=address&k2=8ybguxysus29hapfslbmzttu&',   //<EMAIL>
    4 => '/qdf.php?service=demograph&k2=gzwugz1qnriuxius3mop8s4x&', //<EMAIL>
    5 => '/qdf.php?service=lifeint&k2=tayr7cdrjv8wutxkzqd8elvm&',   //<EMAIL>
    6 => '/qdf.php?service=finhouse&k2=e6jqkt67mcffb3uj8jldublt&',  //<EMAIL>
];


$service = $argv[1];
$testNumber = $argv[2];


switch($service) {
    case 'reach':
        echo "Running test $testNumber, for Reach \n";
        runTest(
            INPUT_FIELDS,
            REACH_TESTS[$testNumber],
            VersiumService::REACH,
            Environment::PROD,
            $testNumber,
        );
        break;
    case 'df':
        echo "Running test $testNumber, for DataFinder \n";
        runTest(
            INPUT_FIELDS,
            DF_TESTS[$testNumber],
            VersiumService::DF,
            Environment::PROD,
            $testNumber,
        );
        break;
    default:
        echo "Please specify a service: reach or df \n";
        exit();
}
