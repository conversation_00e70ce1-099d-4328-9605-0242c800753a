# Compliance Optout Testing Via API

This tool is used to test that the Reach and DataFinder APIs are correctly honoring optouts. It sends requests to the API using a list of records that have been opted out. The tool then checks the response to ensure that the opted out records are not returned. The tests each generate a csv report with details about number of failures, example urls, etc. A second script
parses these csv reports and sends the results to a google spreadsheet. This process is designed to be run on a server via cron, and is entirely automated.

## Prerequisites

1. Create a google service account; Create and download a service account key json file and place this file in the same directory as sendToGSheets.php. (https://cloud.google.com/iam/docs/creating-managing-service-account-keys)
2. Share the google spreadsheet with the service account email.

## Initialization

If running for the first time, install the google api client dependency:
```
composer install
```

## Setup the pipeline in your cron table
The shell scripts are used to orchestrate the pipeline. Open your cron table:
```
crontab -e
```
...and make it look something like this:
```
CRON_TZ=America/Los_Angeles

# delete old optout list (4:50pm, Fri)
50 16 * * 5 cd /home/<USER>/tbull/compliance_testing/ && rm optout.csv && rm optout.txt

# download & format optout list (5pm, Fri)
0 17 * * 5 cd /home/<USER>/tbull/compliance_testing/ && sh download_optout.sh

# run compliance tests against Reach (2am, Mon)
0 2 * * 1 cd /home/<USER>/tbull/compliance_testing/ && sh run_reach.sh  >> ./logs/output_reach.txt

# run compliance tests against DF (2am, Mon)
0 2 * * 1 cd /home/<USER>/tbull/compliance_testing/ && sh run_df.sh  >> ./logs/output_df.txt

# collect results and send to Sheets (9am, Mon)
0 9 * * 1 cd /home/<USER>/tbull/compliance_testing/ && php sendToGSheets.php
```