<?php

/**
 * This script reads and parses compliance testing reports and sends the results to a dedicated Google Sheet for tracking.
 * - If the report name format is changed, this script will need to be updated
 * - Likewise if the spreadsheet structure is changed, you'll want to update prepareValuesArray
 * - Uses a Google service account for authentication. This needs to be setup in the Google Cloud Console
 *
 *   Google APIs Client Library for PHP
 *   https://github.com/googleapis/google-api-php-client
 */

require 'vendor/autoload.php';

const OPTOUT_FAILURES = 'optout_failures';
const SPREADSHEET_ID = '12rhyJ_bdznxS1Z9a_gfK5NH3KHHKc7OL-RcgD7-ui2o';
// const SPREADSHEET_ID_TEST = '1eRtdSA9R4oGgkm9DTqsS5x7tExjLH6rDEHfPzBzx0Js';
const REPORTS_DIR = __DIR__ . '/reports';
const OPTOUT_FILE = './optout.csv';

function main() {
    try {
        $reportResults = collectReportResults();
        $linecount = getOptoutRecordCount();
        $service = initGoogleSheetsService();

        insertNewColumn($service);
        updateSpreadsheetValues($service, $reportResults, $linecount);

        echo "Successfully updated compliance report spreadsheet.\n";
    } catch (Exception $e) {
        logError("Error: " . $e->getMessage());
        exit(1);
    }
}

function collectReportResults() {
    $reportResults = [];

    $files = scandir(REPORTS_DIR);
    if ($files === false) {
        throw new Exception("Failed to scan reports directory");
    }

    $reports = array_filter($files, function($fileName) {
        return !in_array($fileName, ['.', '..']);
    });

    foreach ($reports as $report) {
        $reportPath = REPORTS_DIR . '/' . $report;
        $fp = fopen($reportPath, 'r');
        if ($fp === false) {
            logError("Warning: Could not open report file: $reportPath");
            continue;
        }

        $header = fgetcsv($fp);
        $row2 = fgetcsv($fp);

        // extract failure count
        $failures = (int) filter_var($row2[0], FILTER_SANITIZE_NUMBER_INT);

        // store results
        $reportKey = basename(substr($report, 11), '.csv');
        $reportResults[$reportKey][OPTOUT_FAILURES] = $failures;
        fclose($fp);
    }

    return $reportResults;
}

function getOptoutRecordCount() {
    $fh = fopen(OPTOUT_FILE, 'rb');
    if ($fh === false) {
        throw new Exception("ERROR OPENING OPTOUT DATA");
    }

    $linecount = 0;
    while (fgets($fh) !== false) {
        $linecount++;
    }
    fclose($fh);

    return $linecount;
}

function initGoogleSheetsService() {
    $client = new Google\Client();

    try {
        $client->setAuthConfig('./creds.json');
        $client->setScopes([Google\Service\Sheets::SPREADSHEETS]);
        $client->useApplicationDefaultCredentials();

        return new Google\Service\Sheets($client);
    } catch (Exception $e) {
        throw new Exception("Failed to initialize Google Sheets service: " . $e->getMessage());
    }
}

function insertNewColumn($service) {
    try {
        $requests = [
            new Google\Service\Sheets\Request([
                'insertDimension' => [
                    'range' => [
                        'sheetId' => 0,
                        'dimension' => 'COLUMNS',
                        'startIndex' => 2,
                        'endIndex' => 3
                    ],
                    'inheritFromBefore' => true,
                ]
            ])
        ];

        $batchUpdateRequest = new Google\Service\Sheets\BatchUpdateSpreadsheetRequest([
            'requests' => $requests
        ]);

        $service->spreadsheets->batchUpdate(SPREADSHEET_ID, $batchUpdateRequest);
    } catch (Exception $e) {
        throw new Exception("Failed to insert new column: " . $e->getMessage());
    }
}

function updateSpreadsheetValues($service, $reportResults, $linecount) {
    try {
        $values = prepareValuesArray($reportResults, $linecount);
        $range = 'Optout!C1';

        $body = new Google\Service\Sheets\ValueRange([
            'range' => $range,
            'values' => $values
        ]);

        $params = ['valueInputOption' => 'RAW'];
        $service->spreadsheets_values->update(
            SPREADSHEET_ID,
            $range,
            $body,
            $params
        );
    } catch (Exception $e) {
        throw new Exception("Failed to update spreadsheet values: " . $e->getMessage());
    }
}

function prepareValuesArray($reportResults, $linecount) {
    return [
        [date('Y-m-d')],
        [$linecount - 1],
        [''],
        [''],
        [''],
        [''],
        [''],
        [''],
        [''],
        [''],
        [''],
        [''],
        [ passOrFail($reportResults['Compliance_Reach_1'] ?? []) ],
        [ passOrFail($reportResults['Compliance_Reach_2'] ?? []) ],
        [ passOrFail($reportResults['Compliance_Reach_3'] ?? []) ],
        [ passOrFail($reportResults['Compliance_Reach_4'] ?? []) ],
        [ passOrFail($reportResults['Compliance_Reach_5'] ?? []) ],
        [ passOrFail($reportResults['Compliance_Reach_6'] ?? []) ],
        [''],
        [''],
        [ passOrFail($reportResults['Compliance_DF_1'] ?? []) ],
        [ passOrFail($reportResults['Compliance_DF_2'] ?? []) ],
        [ passOrFail($reportResults['Compliance_DF_3'] ?? []) ],
        [ passOrFail($reportResults['Compliance_DF_4'] ?? []) ],
        [ passOrFail($reportResults['Compliance_DF_5'] ?? []) ],
        [ passOrFail($reportResults['Compliance_DF_6'] ?? []) ],
    ];
}

function passOrFail(array $reportDetails): string {
    if (!isset($reportDetails[OPTOUT_FAILURES])) {
        return 'MISSING';
    }

    $failures = $reportDetails[OPTOUT_FAILURES];

    if (gettype($failures) !== 'integer') {
        return 'Unexpected type: ' . gettype($failures);
    } else {
        return $failures ? 'FAIL x' . $failures : 'PASS';
    }
}

function logError($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";

    // Write to log file
    file_put_contents(__DIR__ . '/gsheets_errors.log', $logMessage, FILE_APPEND);

    // Also output to stderr for cron job emails
    fwrite(STDERR, $logMessage);
}

// Execute the main function
main();
