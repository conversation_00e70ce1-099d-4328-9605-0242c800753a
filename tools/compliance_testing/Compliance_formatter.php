<?php

const HEADER = "email,phone,first,,last,address,,city,state,,zip\n";

function convertTabToComma($inputFile, $outputFile) {
    # Open input file for reading
    $inputHandle = fopen($inputFile, 'r');
    if ($inputHandle === false) {
        die("Failed to open input file.");
    }

    # Open output file for writing
    $outputHandle = fopen($outputFile, 'w');
    if ($outputHandle === false) {
        fclose($inputHandle);
        die("Failed to open output file.");
    }

    # Write header line to output file
    fwrite($outputHandle, HEADER);

    # Read input file line by line and convert tabs to commas
    while (($line = fgets($inputHandle)) !== false) {
        $a = explode("\t", $line);
        $b = array_slice($a, 0, 11);
        fputcsv($outputHandle, $b);
    }

    # Close file handles
    fclose($inputHandle);
    fclose($outputHandle);

    echo "Conversion completed successfully. \n";
}

$inputFile =  '/home/<USER>/tbull/compliance_testing/optout.txt';
$outputFile = '/home/<USER>/tbull/compliance_testing/optout.csv';

convertTabToComma($inputFile, $outputFile);