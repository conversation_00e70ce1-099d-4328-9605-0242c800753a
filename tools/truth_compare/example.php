<?php

/**
 * Example of using TruthCompare
 */

include_once ('truth_compare.php');


$inputs = [
    [
        'truthSet' => 'TS_Skipforce_Phones.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_address,d_city,d_state,d_zip',
        'matchParams' => 'Phone',
        'matchCodes' => '',
        'writeHeader' => true,
    ],
    // [
    //     'truthSet' => 'TS_Skipforce_Phones.csv',
    //     'prodIds' => 'cv',
    //     'inputParams' => 'd_phone',
    //     'matchParams' => 'FirstName,LastName',
    //     'matchCodes' => '',
    //     'writeHeader' => false,
    // ],
    // [
    //     'truthSet' => 'TS_Skipforce_Phones.csv',
    //     'prodIds' => 'cv',
    //     'inputParams' => 'd_first,d_last,d_phone',
    //     'matchParams' => 'Address',
    //     'matchCodes' => '',
    //     'writeHeader' => false,
    // ],
];



foreach ($inputs as $input) {
    TruthCompare(
        $input['truthSet'],
        explode(',', $input['prodIds']),
        explode(',', $input['inputParams']),
        explode(',', $input['matchParams']),
        $input['writeHeader'],
        null,
        null,
        $input['auxParams'] ?? null
    );
}
