<?php

/**
 * Run: php accuracy_report.php [cv|kv]
 */

include_once ('truth_compare.php');

$converseTests = [
    [
        'truthSet' => 'TS_Consumer_Samena_Roster.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_fulladdr,d_city,d_state,d_zip',
        'matchParams' => 'Phone',
        'writeHeader' => true,
    ],
    [
        'truthSet' => 'TS_Consumer_Samena_Roster.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_fulladdr,d_city,d_state,d_zip,d_email',
        'matchParams' => 'Phone',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Consumer_Samena_Roster.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_fulladdr,d_city,d_state,d_zip',
        'matchParams' => 'EmailAddr',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Consumer_Samena_Roster.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_fulladdr,d_city,d_state,d_zip,d_phone',
        'matchParams' => 'EmailAddr',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Consumer_Samena_Roster.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_phone',
        'matchParams' => 'Address',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_People_Phones.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_fulladdr,d_city,d_state,d_zip',
        'matchParams' => 'Phone',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_People_Phones.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_phone',
        'matchParams' => 'FirstName,LastName',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_People_Phones.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_phone',
        'matchParams' => 'Address',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Versium_Internal.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_fulladdr,d_city,d_state,d_zip',
        'matchParams' => 'Phone',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Versium_Internal.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_fulladdr,d_city,d_state,d_zip,d_email',
        'matchParams' => 'Phone',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Versium_Internal.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_fulladdr,d_city,d_state,d_zip',
        'matchParams' => 'EmailAddr',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Versium_Internal.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_fulladdr,d_city,d_state,d_zip,d_phone',
        'matchParams' => 'EmailAddr',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Skipforce_Phones.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_fulladdr,d_city,d_state,d_zip',
        'matchParams' => 'Phone',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Skipforce_Phones.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_phone',
        'matchParams' => 'FirstName,LastName',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Skipforce_Phones.csv',
        'prodIds' => 'cv',
        'inputParams' => 'd_first,d_last,d_phone',
        'matchParams' => 'Address',
        'writeHeader' => false,
    ],
];

$kittiverseTests = [
    [
        'truthSet' => 'TS_Businesses_Large.csv',
        'prodIds' => 'kv',
        'inputParams' => 'd_first,d_last,d_domain',
        'matchParams' => 'CorpName',
        'writeHeader' => true,
    ],
    [
        'truthSet' => 'TS_Businesses_Large.csv',
        'prodIds' => 'kv',
        'inputParams' => 'd_first,d_last,d_busname',
        'matchParams' => 'CorpDomain',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Businesses_Large.csv',
        'prodIds' => 'kv',
        'inputParams' => 'd_liurl,d_busname',
        'matchParams' => 'FirstName,LastName',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Businesses_Small_Medium.csv',
        'prodIds' => 'kv',
        'inputParams' => 'd_first,d_last,d_domain',
        'matchParams' => 'CorpName',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Businesses_Small_Medium.csv',
        'prodIds' => 'kv',
        'inputParams' => 'd_first,d_last,d_busname',
        'matchParams' => 'CorpDomain',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Businesses_Small_Medium.csv',
        'prodIds' => 'kv',
        'inputParams' => 'd_liurl,d_busname',
        'matchParams' => 'FirstName,LastName',
        'writeHeader' => false,
    ],
    [
        'truthSet' => 'TS_Fortune_500.csv',
        'prodIds' => 'kv',
        'inputParams' => 'd_domain',
        'matchParams' => 'CorpName',
        'writeHeader' => false,
        'auxParams' => 'cfg_focus=business&cfg_explain-limit=999999999&cfg_mergemode=,,BusinessHQ&cfg_postsortmode=FillQualityMC&cfg_required=Domain,BusName',
    ],
    [
        'truthSet' => 'TS_Fortune_500.csv',
        'prodIds' => 'kv',
        'inputParams' => 'd_busname',
        'matchParams' => 'CorpDomain',
        'writeHeader' => false,
        'auxParams' => 'cfg_focus=business&cfg_explain-limit=999999999&cfg_mergemode=,,BusinessHQ&cfg_postsortmode=FillQualityMC&cfg_required=Domain,BusName',
    ],
    [
        'truthSet' => 'TS_LinkedIn_Profiles.csv',
        'prodIds' => 'kv',
        'inputParams' => 'd_liurl,d_busname',
        'matchParams' => 'FirstName,LastName',
        'writeHeader' => false,
    ],
];

$db = $argv[1];
$tests = $db == 'cv' ? $converseTests : $kittiverseTests;

foreach ($tests as $test) {
    TruthCompare(
        $test['truthSet'],
        explode(',', $test['prodIds']),
        explode(',', $test['inputParams']),
        explode(',', $test['matchParams']),
        $test['writeHeader'],
        null,
        null,
        $test['auxParams'],
        // null,
        // true // Run in prod!
    );
}