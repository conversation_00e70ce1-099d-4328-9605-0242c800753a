<?php

include_once ('truth_compare_helper.php');

function TruthCompare(
    string $truthSet,
    array $prodIds,
    array $inputParams,
    array $matchParams,
    bool $writeheader = true,
    ?string $matchCodes = null,
    ?string $outputValues = null,
    ?string $auxParams = null,
    ?string $maxRecs = null,
    bool $useProd = false
)
{
    # setup multi_curl
    $mh = curl_multi_init();
    $handles = [];
    $batchSize = 100;

    # results to build up
    $matchResults = [];
    $matchCodeResults = [];
    $sampleUrl = null;
    $errors = 0;

    # read selected truthset
    $fp = fopen('./lists/' . $truthSet, 'r');
    $header = fgetcsv($fp);

    while (($rec = fgetcsv($fp)) !== false) {
        # gather params based on selected inputs
        $inputRec = array_combine($header, $rec);
        $params = getParams($inputRec, $inputParams, $truthSet);
        $reqUrl = createReqUrl($prodIds, $params, $matchCodes, $outputValues, $auxParams, $maxRecs, $useProd);

        # setup curl
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $reqUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_multi_add_handle($mh, $ch);
        # we need to keep track of the input record that is associated with the corresponding curl request
        $handles[] = [$ch, $inputRec];

        # send batch
        if (count($handles) === $batchSize) {
            $batchResponses = makeBatchRequest($mh, $handles, $errors);

            foreach($batchResponses as [$response, $inputRecord]) {
                processResponse($response, $inputRecord, $matchParams, $matchResults, $matchCodeResults);
            }
        }

        if (!$sampleUrl) {
            $sampleUrl = $reqUrl;
        }
    }
    fclose($fp);

    # send and process final batch
    if (count($handles) > 0) {
        $batchResponses = makeBatchRequest($mh, $handles, $errors);
        foreach($batchResponses as [$response, $inputRecord]) {
            processResponse($response, $inputRecord, $matchParams, $matchResults, $matchCodeResults);
        }
    }

    # calc final values
    $totalRecords = TRUTH_SETS[$truthSet]['totalRecords'];
    $matches = $matchResults[0]['matches'] ?: 0;
    $correct = $matchResults[0]['correct'] ?: 0;
    $incorrect = $matches - $correct;
    $matchRate = round(($matches / $totalRecords) * 100, 2);
    $percentCorrect = $matches
        ? round(($correct / $matches) * 100, 2)
        : 0;
    $percentIncorrect = $matches
        ? round(($incorrect / $matches) * 100, 2)
        : 0;

    $tableData = [
        'ProdID'           => implode(',', $prodIds),
        'TS'               => $truthSet,
        'Inputs'           => implode(',', $inputParams),
        'Match Codes'      => $matchCodes ?: 'none',
        'Checking correctness for' => implode(',', $matchParams),
        'Total Recs in TS' => $totalRecords,
        'Total Matches'    => $matches,
        'Match Rate'       => $matchRate . '%',
        'Correct'          => $correct,
        '% Correct'        => $percentCorrect . '%',
        'Incorrect'        => $incorrect,
        '% Incorrect'      => $percentIncorrect . '%',
        'Errors'           => $errors,
        'Sample Url'       => $sampleUrl
    ];

    $csvHeader = array_keys($tableData);
    $csvRow = array_values($tableData);

    # write results to csv
    $filename = './results_' . implode(',', $prodIds) . '_' . date("Y-m-d") . '.csv';
    $fp = fopen($filename, 'a+');
    if ($writeheader) {
        fputcsv($fp, $csvHeader);
    }
    fputcsv($fp, $csvRow);
    fclose($fp);
}

/**
 * Execute batch request and return responses
 *
 * @param CurlMultiHandle $mh
 * @param array $handles
 * @param int $errors
 * @return array Inner arrays have the shape [array $curlResponse, array $inputRec]
 */
function makeBatchRequest($mh, &$handles, &$errors): array
{
    $responses = [];

    # execute multi curl
    $running = null;
    do {
        curl_multi_exec($mh, $running);
    } while ($running);

    # process responses
    foreach ($handles as [$ch, $inputRec]) {
        $res = curl_multi_getcontent($ch);
        $errNum = curl_errno($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($errNum || $status != 200) {
            $errors++;
        } else {
            $responses[] = [json_decode($res, true, 10), $inputRec];
        }
        curl_multi_remove_handle($mh, $ch);
    }
    # empty handles for next batch
    $handles = [];

    return $responses;
}

/**
 * Take a response and corresponding inputRec and determin match/correctness
 *
 * @param array $res Api2b response
 * @param array $inputRec The input record for the api2b response
 * @param array $matchParams The fields we are checking correctness for
 * @param array $matchResults Array to keep track of the number of matches/correct matches we find
 * @param array $matchCodeResults Array to keep track of matchCodes seen
 */
function processResponse($res, $inputRec, $matchParams, &$matchResults, &$matchCodeResults): void
{
    if (!empty($res)) {
        $results = $res['Versium']['results'];

        if (!empty($results)) {
            foreach ($results as $i => $result) {
                $matchCount = 0;
                $correctCount = 0;

                foreach ($matchParams as $inputColumnName) {
                    # handle cases where a result columnName is different than the inputRec columnName
                    $resultColumnName = $inputColumnName;
                    switch($inputColumnName) {
                        case 'BizPersonEmail':
                            $resultColumnName = 'EmailAddr';
                            break;
                        case 'CorpName':
                            $resultColumnName = 'CorpNameKITTB';
                            break;
                    }
                    if (array_key_exists($resultColumnName, $result)) {
                        # found match
                        $matchCount++;

                        # is the match correct?
                        if (isMatchCorrect($inputRec, $inputColumnName, $result, $resultColumnName)) {
                            $correctCount++;
                        } else {
                            # incorrect match, do whatever
                        }
                    }
                }

                $matchParamCount = count($matchParams);
                if ($matchParamCount === $matchCount) {
                    # record match
                    !$matchResults[$i]
                        ? $matchResults[$i]['matches'] = 1
                        : $matchResults[$i]['matches']++;
                }
                if ($matchParamCount === $correctCount) {
                    # record correctness
                    !$matchResults[$i]
                        ? $matchResults[$i]['correct'] = 1
                        : $matchResults[$i]['correct']++;
                }

                # only count match codes from the first result
                if ($i == 0) {
                    if (array_key_exists('#RawMatchCodes', $result)) {
                        $rawCodes = explode(',', $result['#RawMatchCodes']);
                        foreach ($rawCodes as $mc) {
                            array_key_exists($mc, $matchCodeResults)
                                ? $matchCodeResults[$mc]++
                                : $matchCodeResults[$mc] = 1;
                        }
                    }
                }

            }
        }
    }
}

/**
 * Determin if a match is correct
 *
 * @param array $inputRec The orignal inputRecord
 * @param string $inputColumnName The column name to compare from inputRec
 * @param array $result The record returned from api2b
 * @param string $resultColumnName The column name to compare from the result record
 */
function isMatchCorrect($inputRec, $inputColumnName, $result, $resultColumnName): bool
{
    switch($resultColumnName) {
        case 'CorpNameKITTB':
            # do a more specialized correctness check for corpName
            $inputCorpName = preg_replace('/[^A-Za-z0-9\-]/', '', strtolower($inputRec[$inputColumnName]));
            $resultCorpName = preg_replace('/[^A-Za-z0-9\-]/', '', strtolower($result[$resultColumnName]));

            return str_contains($inputCorpName, $resultCorpName) || str_contains($resultCorpName, $inputCorpName);
        default:
            # do "basic" correctness check
            return strtolower($inputRec[$inputColumnName]) == strtolower($result[$resultColumnName]);
    }
}

function getParams(array $inputRec, array $inputParams, string $truthSet): array
{
    $params = [];
    $truthSetHeaderMap = TRUTH_SETS[$truthSet]['headerMap'];

    // TODO: fix "undefined array key" warning here. Try ?? instead of isset()?
    foreach($inputParams as $param) {
        if (isset($truthSetHeaderMap[$param])) {
            $params[$param] = $inputRec[$truthSetHeaderMap[$param]];
        }
        // $params[$param] = $inputRec[$truthSetHeaderMap[$param]];
    }

    return $params;
}

function createReqUrl(
    array $prodIds,
    array $params = [],
    ?string $matchCodes = null,
    ?string $outputs = null,
    ?string $auxParams = null,
    ?string $maxRecs = null,
    bool $prod = false
    ): string
{
    $baseParams = [
        'vkey' => VKEY,
        'prodids' => implode(',', $prodIds),
        'cfg_maxrecs' => $maxRecs ?? 1,
        'cacheBuster' => rand(), # send random value to avoid cached results
        'cfg_output' => 'basic,stats2,' . implode(',', $prodIds) . ',' . ($outputs ?? ''),
    ];

    # apply aux params
    if ($auxParams) {
        $splitParams = explode('&', $auxParams);
        foreach ($splitParams as $param) {
            $p = explode('=', $param);
            $baseParams[$p[0]] = $p[1];
        }
    }
    # apply match codes
    if ($matchCodes) {
        $baseParams['cfg_mc'] = $matchCodes;
    }

    return $prod
        ? BASE_URL_PROD . http_build_query(array_merge($baseParams, $params))
        : BASE_URL . http_build_query(array_merge($baseParams, $params));
}
