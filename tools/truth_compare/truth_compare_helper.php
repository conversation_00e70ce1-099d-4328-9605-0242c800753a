<?php


const BASE_URL = 'https://api2b-stg.versium.com/q2.php?';
const BASE_URL_PROD = 'https://api2b.versium.com/q2.php?';
const VKEY = '6f1ba41224002559d0ae8539d334d6b2';

# not currently using this (but its handy)
const COLUMN_NAME_TO_MATCH_CODE = [
  'FirstName' => 'F0',
  'LastName' => 'L0',
  'Address' => 'AS0',
  'City' => 'C0',
  'State' => 'S0',
  'Zip' => 'Z0',
  'EmailAddr' => 'E0',
  'Phone' => 'P0',
  'CorpName' => 'B0',
  'CorpDomain' => 'DOM0',
  'CorpAddress' => 'AS0',
  'CorpCity' => 'C0',
  'CorpState' => 'S0',
  'BizPersonEmail' => 'E0',
  'CorpPhone' => 'P0',
  'CorpEmailAddr' => 'E0',
];

const BUS_HEADER_MAP = [
  'd_first' => 'FirstName',
  'd_last' => 'LastName',
  'd_fulladdr' => 'CorpAddress',
  'd_city' => 'CorpCity',
  'd_state' => 'CorpState',
  'd_zip' => 'CorpZip',
  'd_email' => 'BizPersonEmail',
  'd_phone' => 'CorpPhone',
  'd_busname' => 'CorpName',
  'd_domain' => 'CorpDomain',
  'd_liurl' => 'LIProfileURL',
];

const CONSUMER_HEADER_MAP = [
  'd_first' => 'FirstName',
  'd_last' => 'LastName',
  'd_fulladdr' => 'Address',
  'd_city' => 'City',
  'd_state' => 'State',
  'd_zip' => 'Zip',
  'd_email' => 'EmailAddr',
  'd_phone' => 'Phone',
  'd_busname' => 'CorpName',
  'd_domain' => 'CorpDomain',
  'd_liurl' => 'LIProfileURL',
];

const TRUTH_SETS = [
  'TS_Businesses_Large.csv' => [
      'headerMap' => BUS_HEADER_MAP,
      'totalRecords' => 102,
  ],
  'TS_Businesses_Small_Medium.csv' => [
      'headerMap' => BUS_HEADER_MAP,
      'totalRecords' => 100,
  ],
  'TS_Fortune_500.csv' => [
      'headerMap' => BUS_HEADER_MAP,
      'totalRecords' => 500,
  ],
  'TS_Salesforce_BizPersonEmails.csv' => [
      'headerMap' => [
          'd_first'   => 'FirstName',
          'd_last'    => 'LastName',
          'd_phone'   => 'Phone',
          'd_email'   => 'BizPersonEmail',
          'd_busname' => 'CorpName',
      ],
      'totalRecords' => 852,
  ],
  'TS_Hubspot_BizPersonEmails.csv' => [
      'headerMap' => [
          'd_first'    => 'FirstName',
          'd_last'     => 'LastName',
          'd_fulladdr' => 'CorpAddress',
          'd_city'     => 'CorpCity',
          'd_state'    => 'CorpState',
          'd_zip'      => 'CorpZip',
          'd_phone'    => 'Phone',
          'd_email'    => 'BizPersonEmail',
          'd_busname'  => 'CorpName',
          'd_domain'   => 'CorpDomain',
          'd_liurl'    => 'LIProfileURL',
      ],
      'totalRecords' => 105,
  ],
  'TS_Consumer_Samena_Roster.csv' => [
      'headerMap' => CONSUMER_HEADER_MAP,
      'totalRecords' => 49,
  ],
  'TS_LinkedIn_Profiles.csv' => [
      'headerMap' => CONSUMER_HEADER_MAP,
      'totalRecords' => 139,
  ],
  'TS_Versium_Opt_Out_Feb_2023.csv' => [
      'headerMap' => CONSUMER_HEADER_MAP,
      'totalRecords' => 695,
  ],
  'TS_People_Phones.csv' => [
      'headerMap' => CONSUMER_HEADER_MAP,
      'totalRecords' => 1501,
  ],
  'TS_Versium_Internal.csv' => [
      'headerMap' => CONSUMER_HEADER_MAP,
      'totalRecords' => 198,
  ],
  'TS_Skipforce_Phones.csv' => [
      'headerMap' => [
            'd_first'    => 'FirstName',
            'd_last'     => 'LastName',
            'd_fulladdr' => 'MailingAddress',
            'd_city'     => 'MailingCity',
            'd_state'    => 'MailingState',
            'd_zip'      => 'MailingZip',
            'd_email'    => 'EmailAddr',
            'd_phone'    => 'Phone',
      ],
      'totalRecords' => 4962,
  ],
];