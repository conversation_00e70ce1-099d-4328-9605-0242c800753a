# Truth Compare Tool

A PHP-based testing tool for validating the accuracy of Versium's API2B data enrichment services against known truth sets.

## Overview

This tool compares API responses from Versium's data enrichment API against curated "truth sets" (CSV files containing verified data) to measure match rates and accuracy. It supports testing both staging and production environments for different product IDs (cv, kv, etc.).

## Key Features

- **Batch Processing**: Uses cURL multi-handle for efficient parallel API requests
- **Multiple Truth Sets**: Supports various data types including consumer data, business data, Fortune 500 companies, and more
- **Accuracy Metrics**: Calculates match rates, correctness percentages, and error counts
- **Flexible Configuration**: Customizable input parameters, match codes, and output values
- **CSV Reporting**: Generates detailed results in CSV format with timestamps

## Usage

### Basic Example
```php
include_once ('truth_compare.php');

TruthCompare(
    'TS_Consumer_Samena_Roster.csv',  // Truth set file
    ['cv'],                           // Product IDs to test
    ['d_first', 'd_last', 'd_phone'], // Input parameters
    ['EmailAddr'],                    // Fields to check for correctness
    true                              // Write CSV header
);
```

### Running Predefined Tests
We have two have predefined reports, one for Converse (cv) and one for Kittiverse (kv).
```bash
# Run Converse (cv) accuracy tests
php accuracy_report.php cv

# Run Kittiverse (kv) accuracy tests
php accuracy_report.php kv
```

## Output

Results are saved to CSV files named `results_{prodId}_{date}.csv` containing:
- Product ID and truth set used
- Input parameters and match codes
- Total records, matches, and match rate
- Correct/incorrect counts and percentages
- Error count and sample API URL

## Configuration

- **API Endpoints**: Configured for both staging and production Versium API2B
- **Truth Set Mappings**: Each truth set has a header mapping to standardize field names
- **Match Code Support**: Optional match codes for refined matching logic
- **Correctness Logic**: Specialized validation for different data types (e.g., fuzzy matching for company names)

This tool is essential for quality assurance and performance monitoring of Versium's data enrichment services.