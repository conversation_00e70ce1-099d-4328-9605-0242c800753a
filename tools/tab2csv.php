<?php


function tab2csv($inputFile, $outputFile) {
    # Open input file for reading
    $inputHandle = fopen($inputFile, 'r');
    if ($inputHandle === false) {
        die("Failed to open input file.");
    }

    # Open output file for writing
    $outputHandle = fopen($outputFile, 'w');
    if ($outputHandle === false) {
        fclose($inputHandle);
        die("Failed to open output file.");
    }

    # Read input file line by line and convert tabs to commas
    while (($line = fgets($inputHandle)) !== false) {
        $a = explode("\t", $line);
        fputcsv($outputHandle, $a);
    }

    # Close file handles
    fclose($inputHandle);
    fclose($outputHandle);

    echo "Conversion completed successfully.";
}

$input = $argv[1];
$output = './output.csv';

tab2csv($input, $output);