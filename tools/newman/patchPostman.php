<?php

// === Config ===
$inputFile  = './tests/Reach-APIs.postman_collection.json';           // Path to your exported Postman collection
$outputFile = './tests/Reach-APIs.postman_collection_patched.json';   // Output file path
$variableName = 'csvFilePath';                                        // The name of the variable to use in {{}} syntax

// === Load JSON ===
$json = json_decode(file_get_contents($inputFile), true);
if (json_last_error() !== JSON_ERROR_NONE) {
    exit("Failed to parse JSON: " . json_last_error_msg() . PHP_EOL);
}

// === Recursive function to patch "src" values ===
function patchSrc(&$item, $variableName) {
    if (is_array($item)) {
        foreach ($item as $key => &$value) {
            // Look for src with postman-cloud:// prefix
            if ($key === 'src' && is_string($value) && str_starts_with($value, 'postman-cloud://')) {
                $value = "{{{$variableName}}}";
            } else {
                patchSrc($value, $variableName);
            }
        }
    }
}

// === Patch the collection ===
patchSrc($json, $variableName);

// === Save the patched JSON ===
file_put_contents($outputFile, json_encode($json, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
echo "Patched file saved as: $outputFile" . PHP_EOL;
