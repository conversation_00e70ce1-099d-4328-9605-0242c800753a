<?php
/**
 * Created by PhpStorm.
 * User: gchandramouli
 * Date: 12/12/18
 * Time: 12:53 PM
 */

define("ERROR", "error");
define("RESULT", "result");


define("PRODID", "prodids");
define("CFG_MAX_RECORDS", "cfg_maxrecs");

define("K", 'vkey');
define("CV_KEY",'6f1ba41224002559d0ae8539d334d6b2');
define("CFG_OUTPUT", "cfg_output");
define("CFG_MATCH", "fg_mc");
define("OUTPUT_CV", "basic,stats2,cv");
define("CV_PRODID", "cv");
//define("MC_INDIV", "INDIV");
define("MC_INDIV", "INDIV;HHLD;H0,L;O0,L;IP0,L;E0,L;P0,L;PINDIV3;PINDIV4;LF0,AHN0,CS;LF0,AHN0,Z0;LF0,AS0,CS;LF0,AS0,Z0;LF,D,S");
define("MC_HHLD", "HHLD");

define("CFG_FOCUS", "cfg_focus");

//$main_url = "https://api2b.versium.com/q2.php?vkey=6f1ba41224002559d0ae8539d334d6b2&d_first=SAMANTHA&d_last=AAENSON&d_fulladdr=20310%20Bothell%20Everett%20Hwy%20Apt%20B1&d_city=Bothell&d_state=WA&d_zip=98012&prodids=cv&cfg_mc=INDIV&cfg_output=cv


define("FIRST_NAME", "d_first");
define("LAST_NAME", "d_last");
define("ADDRESS", "d_fulladdr");
define("CITY", "d_city");
define("STATE", "d_state");
define("ZIP", "d_zip");
define("PHONE", "d_phone");
define("EMAIL", "d_email");

define("OUTPUT_PHONE","Phone");
define("OUTPUT_EMAIL","EmailAddr");

define("CMD","cmd");
define("READ_ALL","readall");
define("TICKER","ticker");
define("DATA","data");


class TestRunner
{
    protected $url;
    protected $result;
    protected $startTime;
    protected $endTime;
    protected $output;
    protected $passed;

    public $input_output_mapping;
    public $output_input_mapping;


    protected function csv_to_array($filename = '', $delimiter = ',')
    {
        //if(!file_exists($filename) || !is_readable($filename))
        //   return FALSE;

        $header = null;
        $data = [];//array();
        if (($handle = fopen($filename, 'r')) !== false) {
            while (($row = fgetcsv($handle, 1000, $delimiter)) !== false) {
                if (!$header) {
                    $header = array_map('trim', $row);
                } else {
                    $data[] = array_combine($header, $row);
                }
            }
            fclose($handle);
        }

        return $data;
    }

    function CallAPI($method, &$url, $data = false)
    {

        $response = [];

        $curl = curl_init();

        switch ($method) {
            case "POST":
                curl_setopt($curl, CURLOPT_POST, 1);

                if ($data) {
                    curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
                }
                break;
            case "PUT":
                curl_setopt($curl, CURLOPT_PUT, 1);
                break;
            default:
                if ($data) {
                    $url = sprintf("%s?%s", $url, http_build_query($data));
                }
        }

        //print_r($url);


        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        $result = curl_exec($curl);

        if ($result === false) {
            $response[ERROR] = "CURL Error: " . curl_error($curl);

        }


        $responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        if ($responseCode >= 400) {
            $response[ERROR] = "HTTP Error: " . $responseCode;
        }


        curl_close($curl);

        //print_r($response[ERROR]);
        $response[RESULT] = json_decode($result, true, 10);
        //print_r($result);

        return $response;
    }


    protected function runTest(&$url, $params)
    {
        return $this->CallAPI("GET", $url, $params);

    }

    protected function getParams($record, $inputParamCombination)
    {
        $params = [
            K => CV_KEY,
            PRODID => CV_PRODID,
            CFG_OUTPUT => OUTPUT_CV,
            //CFG_MATCH => MC_HHLD,
            // CFG_MATCH => MC_INDIV,
            //'cfg_dbid' => 'shdev',
        ];

        print_r($record);
        foreach ($inputParamCombination as $param) {
            $params[$param] = $record[$param];
        }

        return $params;
    }


    public function getEmptyTestRecord($outputFields)
    {
        $test['url'] = ' ';
        // $test['num-results'] = ' ';
        foreach ($outputFields as $outputField) {
            $test[$outputField] = ' ';
        }
        return $test;
    }


    public function runTests(
        $url,
        $inputParamCombinations, // [0] => 'label', [1] => inputs[]
        $outputFields,
        $inputFileName
    ) {
        // $label = $inputParamCombinations[0];
        // $inputs = $inputParamCombinations[1];

        $data = $this->csv_to_array($inputFileName);

        $csvFileName = 'output' . time() . $inputParamCombinations[0] . '.csv';
        // $csvFileName = 'output' . time() . '.csv';
        $fp = fopen($csvFileName, 'w');

        $test = $this->getEmptyTestRecord($outputFields);

        fputcsv($fp, array_keys($test));
        foreach ($data as $record) {
            // foreach ($inputParamCombinations as $inputParamCombination) {
                //print_r($record);
                $tempURL = $url;
                $params = $this->getParams($record, $inputParamCombinations[1]);
                print_r($params);
                $test = $this->getEmptyTestRecord($outputFields, $record);
                $response = $this->runTest($tempURL, $params);

                print_r($response);

                $test['url'] = '=HYPERLINK("' . urldecode($tempURL) . '")';

                if (!isset($response[ERROR])) {
                    $output = $response[RESULT];

                    if(isset($output['Versium']['results'])) {
                        $rec = $output['Versium']['results'][0];
                        print_r($rec);

                        foreach ($outputFields as $outputField) {
                            if (isset($rec[$outputField])) {
                                print_r($rec[$outputField]);
                                $test[$outputField] = $rec[$outputField];
                            } else {
                                $test[$outputField] = '';
                            }
                        }
                    }
                }
                print_r($test);
                fputcsv($fp, $test);
            // }
            // break;
        }

        fclose($fp);
        print_r("The report generated in : " . $csvFileName);
    }
}

//$main_url = "https://api2b.versium.com/cv.php";
//$main_url = "https://api2b.versium.com/q2.php?vkey=6f1ba41224002559d0ae8539d334d6b2&d_first=SAMANTHA&d_last=AAENSON&d_fulladdr=20310%20Bothell%20Everett%20Hwy%20Apt%20B1&d_city=Bothell&d_state=WA&d_zip=98012&prodids=cv&cfg_mc=INDIV&cfg_output=cv
$main_url = "https://api2b-stg.versium.com/q2.php";

// $inputParamCombinations = [
//     [FIRST_NAME, LAST_NAME, ADDRESS, CITY, STATE, ZIP],
// ];
$inputParamCombinations = [
    ['FL+Addr',       [FIRST_NAME, LAST_NAME, ADDRESS, CITY, STATE, ZIP]],
    ['FL+Addr+Email', [FIRST_NAME, LAST_NAME, ADDRESS, CITY, STATE, ZIP, EMAIL]],
    ['FL+Addr+Phone', [FIRST_NAME, LAST_NAME, ADDRESS, CITY, STATE, ZIP, PHONE]],
    ['Phone',         [PHONE]]
];

//$fileName = "1.csv";
$fileName = "TruthSet-Blanchet.csv";

$outputFields = [
    'FirstName',
    'LastName',
    'Address',
    'City',
    'State',
    'Zip',
    'Phone',
    'EmailAddr'
];


$testRunner = new TestRunner();

// $testRunner->runTests($main_url, $inputParamCombinations, $outputFields, $fileName);

foreach ($inputParamCombinations as $inputCombination) {
    $testRunner->runTests(
        $main_url,
        $inputCombination,
        $outputFields,
        $fileName
    );
}
