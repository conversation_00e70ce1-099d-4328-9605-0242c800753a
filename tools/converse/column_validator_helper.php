<?php

$ethnicCodes = [
    "00" => "Unknown",
    "C1" => "Afghani",
    "C2" => "Bengladesh",
    "C3" => "Indian",
    "C4" => "Pakistani",
    "C5" => "Sri Lankan",
    "C6" => "Nepal",
    "C7" => "Telugan",
    "D0" => "Algerian",
    "D1" => "Arab",
    "D2" => "Bahrain",
    "D3" => "Egyptian",
    "D4" => "Greek",
    "D5" => "Iraqi",
    "D6" => "Kurdish",
    "D7" => "Jewish",
    "D8" => "Kuwaiti",
    "D9" => "Libyan",
    "DE" => "Macedonian",
    "DF" => "Moroccan",
    "DG" => "Qatar",
    "DH" => "Persian",
    "DJ" => "Saudi",
    "DK" => "Syrian",
    "DL" => "Tunisian",
    "DM" => "Turkish",
    "DN" => "Yemeni",
    "DS" => "Maltese",
    "KS" => "Native American",
    "M0" => "African American 1",
    "M1" => "Angolan",
    "M2" => "Ashanti",
    "M3" => "Basotho",
    "M4" => "Benin",
    "M5" => "Bhutanese",
    "M6" => "Burkina Faso",
    "M7" => "Burundi",
    "M8" => "Cameroon",
    "M9" => "Cent Afric Rep",
    "MA" => "Chad",
    "MB" => "Comoros",
    "MC" => "Congo",
    "MD" => "Equat Guinea",
    "ME" => "Ethiopian",
    "MF" => "Gabon",
    "MG" => "Gambia",
    "MH" => "Ghana",
    "MJ" => "Guinea-Bissea",
    "MK" => "Guyana",
    "ML" => "Ivory Coast",
    "MM" => "Kenya",
    "MN" => "Lesotho",
    "MO" => "Liberian",
    "MP" => "Madagascar",
    "MQ" => "Malawi",
    "MR" => "Mali",
    "MS" => "Namibian",
    "MT" => "Nigerian",
    "MU" => "Mozambique",
    "MV" => "Papua New Guinea",
    "MW" => "Ruandan",
    "MX" => "Senegalese",
    "MY" => "Siere Leone",
    "MZ" => "Somalia",
    "N1" => "Danish",
    "N2" => "Dutch",
    "N3" => "Finnish",
    "N4" => "Icelandic",
    "N5" => "Norwegian",
    "N6" => "Scotch",
    "N7" => "Swedish",
    "N8" => "Welsh",
    "R1" => "Aleut",
    "R2" => "Myanmar",
    "R3" => "Chinese",
    "R4" => "Fiji",
    "R5" => "Hawaiian",
    "R6" => "Indonesian",
    "R7" => "Japanese",
    "R8" => "Khmer",
    "R9" => "Korean",
    "RA" => "Laotian",
    "RB" => "Malay",
    "RC" => "Mongolian",
    "RD" => "Other Asian",
    "RE" => "Filipino",
    "RF" => "Thai",
    "RG" => "Tibetan",
    "RH" => "Vietnamese",
    "RJ" => "Maldivian",
    "RK" => "Nauruan",
    "RM" => "New Zealand",
    "RP" => "Australian",
    "RQ" => "Vanuatuan",
    "RS" => "Pili",
    "T1" => "Belgian",
    "T2" => "Basque",
    "T3" => "English",
    "T4" => "French",
    "T5" => "German",
    "T6" => "Irish",
    "T7" => "Italian",
    "T8" => "Portuguese",
    "T9" => "Hispanic",
    "TE" => "Liechtenstein",
    "TF" => "Luxembourgian",
    "TH" => "Swiss",
    "TJ" => "Manx",
    "U0" => "Albanian",
    "U1" => "Armenian",
    "U2" => "Austrian",
    "U3" => "Azerb",
    "U4" => "Bosnian",
    "U5" => "Bulgarian",
    "U6" => "Byelorussian",
    "U7" => "Chechnian",
    "U8" => "Croatian",
    "U9" => "Czech",
    "UA" => "Estonian",
    "UB" => "Georgian",
    "UC" => "Hungarian",
    "UD" => "Kazakh",
    "UE" => "Kirghiz",
    "UF" => "Kyrgyzstani",
    "UG" => "Latvian",
    "UH" => "Lithuanian",
    "UI" => "Moldavian",
    "UJ" => "Polish",
    "UK" => "Romanian",
    "UL" => "Russian",
    "UM" => "Serbian",
    "UN" => "Slovakian",
    "UP" => "Slovenian",
    "UQ" => "Tajikistan",
    "UR" => "Tajik",
    "UT" => "Turkmenistan",
    "UV" => "Ukrainian",
    "UW" => "Uzbekistani",
    "W0" => "South African",
    "W1" => "Surinam",
    "W2" => "Sudanese",
    "W3" => "Swaziland",
    "W4" => "Tanzanian",
    "W5" => "Togo",
    "W6" => "Tonga",
    "W7" => "Ugandan",
    "W8" => "Xhosa",
    "W9" => "Zaire",
    "WA" => "Zambian",
    "WB" => "Zimbabwe",
    "WC" => "Zulu",
    "WE" => "Djibouti",
    "WF" => "Guinean",
    "WG" => "Mauritania",
    "WH" => "Niger",
    "WJ" => "Seychelles",
    "WK" => "Western Samoa",
    "WL" => "African American 2",
    "WM" => "Botswanian",
    "WN" => "Hausa",
    "WP" => "Caribbean African American",
    "WS" => "Swahili",
    "XX" => "Multi-Ethnic"
];

$languageCodes = [
    "A1" => "Afrikaans",
    "A2" => "Albanian",
    "A3" => "Amharic",
    "A4" => "Arabic",
    "A5" => "Armenian",
    "A6" => "Ashanti",
    "A7" => "Azeri",
    "B1" => "Bantu",
    "B2" => "Basque",
    "B3" => "Bengali",
    "B4" => "Bulgarian",
    "B5" => "Burmese",
    "C1" => "Chinese (Mandarin, Cantonese and other dialects)",
    "C2" => "Comorian",
    "C3" => "Czech",
    "D1" => "Danish",
    "D2" => "Dutch",
    "D3" => "Dzongha",
    "E1" => "English",
    "E2" => "Estonian",
    "F1" => "Farsi",
    "F2" => "Finnish",
    "F3" => "French",
    "G1" => "Georgian",
    "G2" => "German",
    "G3" => "Ga",
    "G4" => "Greek",
    "H1" => "Hausa",
    "H2" => "Hebrew",
    "H3" => "Hindi",
    "H4" => "Hungarian",
    "I1" => "Icelandic",
    "I2" => "Indonesian",
    "I3" => "Italian",
    "J1" => "Japanese",
    "K1" => "Kazakh",
    "K2" => "Khmer",
    "K3" => "Kirghiz",
    "K4" => "Korean",
    "L1" => "Laotian (Include Hmong)",
    "L2" => "Latvian",
    "L3" => "Lithuanian",
    "M1" => "Macedonian",
    "M2" => "Malagasy",
    "M3" => "Malay",
    "M4" => "Moldavian",
    "M5" => "Mongolian",
    "N1" => "Nepali",
    "N2" => "Norwegian",
    "O1" => "Oromo",
    "P1" => "Pashto",
    "P2" => "Polish",
    "P3" => "Portuguese",
    "R1" => "Romanian",
    "R2" => "Russian",
    "S1" => "Samoan",
    "S2" => "Serbo-Croatian",
    "S3" => "Sinhalese",
    "S4" => "Slovakian",
    "S5" => "Slovenian",
    "S6" => "Somali",
    "S7" => "Sotho",
    "S8" => "Spanish",
    "S9" => "Swahili",
    "SA" => "Swazi",
    "SB" => "Swedish",
    "T1" => "Tagalog",
    "T2" => "Tajik",
    "T3" => "Thai",
    "T4" => "Tibetan",
    "T5" => "Tongan",
    "T6" => "Turkish",
    "T7" => "Turkmeni",
    "T8" => "Tswana",
    "UX" => "Unknown",
    "U1" => "Urdu",
    "U2" => "Uzbeki",
    "V1" => "Vietnamese",
    "X1" => "Xhosa",
    "Z1" => "Zulu"
];

$homeValues = [
    "A" => "$1,000-24,999",
    "B" => "$25,000-49,999",
    "C" => "$50,000-74,999",
    "D" => "$75,000-99,999",
    "E" => "$100,000-124,999",
    "F" => "$125,000-149,999",
    "G" => "$150,000-174,999",
    "H" => "$175,000-199,999",
    "I" => "$200,000-224,999",
    "J" => "$225,000-249,999",
    "K" => "$250,000-274,999",
    "L" => "$275,000-299,999",
    "M" => "$300,000-349,999",
    "N" => "$350,000-399,999",
    "O" => "$400,000-449,999",
    "P" => "$450,000-499,999",
    "Q" => "$500,000-749,999",
    "R" => "$750,000-999,999",
    "S" => "> $1,000,000"
];

$hispanicCountryCodes = [
    "HA" => "Argentina",
    "HB" => "Bolivia",
    "HZ" => "Brazil",
    "HQ" => "Chile",
    "HJ" => "Colombia",
    "HR" => "Costa Rica",
    "HC" => "Cuba",
    "HD" => "Dominican Republic",
    "HL" => "Ecuador",
    "HE" => "El Salvador",
    "HG" => "Guatemala",
    "HH" => "Honduras",
    "HM" => "Mexico",
    "HN" => "Nicaragua",
    "HK" => "Panama",
    "HY" => "Paraguay",
    "HX" => "Peru",
    "HP" => "Puerto Rico",
    "HS" => "Spain",
    "HU" => "Uruguay",
    "HV" => "Venezuela",
    "00" => "Unknown"
];

$ethnicGroups = [
    "F" => "All African American Ethnic Groups",
    "O" => "Far Eastern",
    "A" => "Southeast Asian",
    "C" => "Central & Southwest Asian",
    "M" => "Mediterranean",
    "N" => "Native American",
    "S" => "Scandinavian",
    "P" => "Polynesian",
    "I" => "Middle Eastern",
    "J" => "Jewish",
    "W" => "Western European",
    "E" => "Eastern European",
    "U" => "Miscellaneous Other",
    "Z" => "Uncoded (No Group)",
    "Y" => "Hispanic",
    "T" => "Other"
];

$mortgageMostRecentLoanTypeCode = [
    "5" => "COMMUNITY DEVLOPMENT AUTHORITY",
    "C" => "CONVENTIONAL",
    "F" => "FHA",
    "P" => "PRIVATE PARTY LENDER",
    "S" => "SMALL BUSINESS ADMINISTRATION",
    "V" => "VA",
    "W" => "WRAP-AROUND MORTGAGE"
];

$creditRating = [
    "A" => "800+",
    "B" => "750-799",
    "C" => "700-749",
    "D" => "650-699",
    "E" => "600-649",
    "F" => "550-599",
    "G" => "500-549",
    "H" => "< 499"
];

$religion = [
    "B" => "Buddhist",
    "C" => "Catholic",
    "G" => "Greek Orthodox",
    "H" => "Hindu",
    "I" => "Islamic",
    "J" => "Jewish",
    "K" => "Sikh",
    "L" => "Lutheran",
    "M" => "Mormon",
    "O" => "Eastern Orthodox",
    "P" => "Protestant",
    "S" => "Shinto",
    "X" => "Not Known or Unmatched"
];

$homeFuel = [
    'E' => 'Electric',
    'G' => 'Gas',
    'P' => 'Gas Public / Piped',
    'O' => 'Oil',
];

$homeSewer = [
    '1' => 'Sewer - Commercial',
    '2' => 'Sewer - Private',
    '3' => 'Sewer - Public',
    '4' => 'Sewer - Septic'
];

$homeWater = [
    '1' => 'Water - Commercial',
    '2' => 'Water - Private',
    '3' => 'Water - Public',
    '4' => 'Water - Well',
];

$homeSalesTransactionCode = [
    'C' => 'Construction Loan',
    'N' => 'Subdivision / New Construction',
    'R' => 'Resale',
    'S' => 'Seller Carryback'
];

$refinanceRateType = [
    'A' => 'Adjustable',
    'B' => 'Balloon',
    'F' => 'Fixed',
    'U' => ''
];

$refinanceLoanType = [
   '5' => 'Community Development Authority',
   'C' => 'Conventional',
   'F' => 'FHA',
   'P' => 'Private Party Lender',
   'S' => 'Small Business Administration',
   "V" => "VA",
   'W' => 'Wrap-Around Mortgage'
];

$mortgageMostRecentLoanTypeCode = [
    "5" => "COMMUNITY DEVLOPMENT AUTHORITY",
    "C" => "CONVENTIONAL",
    "F" => "FHA",
    "P" => "PRIVATE PARTY LENDER",
    "S" => "SMALL BUSINESS ADMINISTRATION",
    "V" => "VA",
    "W" => "WRAP-AROUND MORTGAGE"
];

$airConditioning = [
    'A' => 'AC - Central',
    'B' => 'AC - Central & Unit',
    'C' => 'AC - Dual Unit',
    'D' => 'AC - Evaporative',
    'E' => 'AC - Heat Pump',
    'F' => 'AC - Refrigeration',
    'G' => 'AC - Package',
    'H' => 'AC - Separate System',
    'I' => 'AC - Split System',
    'J' => 'AC - Wall Unit',
    'K' => 'AC - Window Unit',
    'L' => 'AC - Wall/Window Unit'
];