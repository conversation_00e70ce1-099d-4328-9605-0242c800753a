# Converse Data Integrity Testing

The column_validator tool is used to validate the integrity of the data in Converse. It can be used to validate the following:
- Column names
- Value types
- Value formats
- Value content

We start by taking a sample of the data in Converse. Remember to sample both versions on Converse.
StarniumHoldings.Converse is the append build, Converse.Converse is the listGen build.
The sample is taken using the following query:

```
SELECT *
FROM Converse
WHERE MOD(CONV(SUBSTRING(MD5(`STHHLD`), 1, 8), 16, 10), 1000) = 1
LIMIT 5000;
```

The sample should be exported to a csv file and placed in the `/converse/lists` directory.

Run the column_validator tool and observe the console output:

```
php column_validator.php
```