<?php

# TODO: Determin the best format for reporting the failures.
# TODO: Track column level stats, like
    # null % rate
    # regex match rate
    # common failure values
# TODO: Check referential integrity? (zip is in State; city is in state)

include_once 'column_validator_ruleset.php';
include_once 'column_validator_helper.php';

$fileName = "cv_slice_1000_5_5000.csv";

# read in the Converse sample data
$path = "./samples/$fileName";
$fp = fopen($path, 'r');
$headerRow = fgetcsv($fp);

# Check that the number of columns in the sample matches the number of tests
$headerCount = count($headerRow);
$testCount = count(array_keys($columnValueTests));

if ($headerCount !== $testCount) {
    echo "Warning: Column count / Test count mismatch. There are $headerCount Converse header columns and $testCount number of tests. \n";
    die();
}


$failures = [];

while (($rec = fgetcsv($fp)) !== false) {

    $fullRecord = array_combine($headerRow, $rec);

    # iterate through column headers
    foreach ($headerRow as $columnHeader) {

        # if a test exists for the column, perform it
        if (array_key_exists($columnHeader, $columnValueTests)) {
            $test = $columnValueTests[$columnHeader];
            $value = $fullRecord[$columnHeader];

            if ($test['canBeEmpty'] && empty($value)) {
                continue;
            }

            if ($test['pattern'] === null) {
                continue;
            }

            if ($test['canBeEmpty'] === false && $value === '') {
                echo "Failed value test for $columnHeader; An Empty value was found. \n";
                $failures[] = [
                    'column' => $columnHeader,
                    'value' => "Empty when canBeEmpty is false.",
                    'pattern' => $test['pattern'],
                    'STHHLD' => $fullRecord['STHHLD'],
                ];
                continue;
            }

            switch (gettype($test['pattern'])) {
                case 'string':
                    if (!preg_match($test['pattern'], $value)) {
                        echo "Failed value test for $columnHeader with value $value \n";
                        $failures[] = [
                            'column' => $columnHeader,
                            'value' => $value,
                            'pattern' => $test['pattern'],
                            'STHHLD' => $fullRecord['STHHLD'],
                        ];
                    }
                    break;
                case 'array':
                    if (!in_array($value, $test['pattern'])) {
                        echo "Failed value test for $columnHeader with value $value \n";
                        $failures[] = [
                            'column' => $columnHeader,
                            'value' => $value,
                            'pattern' => $test['pattern'],
                            'STHHLD' => $fullRecord['STHHLD'],
                        ];
                    }
                    break;
                default:
            }
        }
    }
}

file_put_contents('validation_failures.json', json_encode($failures, JSON_PRETTY_PRINT));

echo "Tests complete.\n";