<?php


// const BASE_URL_PROD = 'https://api2b.versium.com/q2.php?vkey=6f1ba41224002559d0ae8539d334d6b2&';
// const BASE_URL_STG = 'https://api2b-stg.versium.com/q2.php?vkey=6f1ba41224002559d0ae8539d334d6b2&';
const BASE_URL_PROD = 'https://api2b.versium.com/q2.php?k=nhwuzas5fsfh-phone&';     # for multiPhone
const BASE_URL_STG =  'https://api2b-stg.versium.com/q2.php?k=nhwuzas5fsfh-phone&'; # for multiPhone

const BUS_HEADER_MAP = [
    'FirstName' => 'd_first',
    'LastName' => 'd_last',
    'CorpAddress' => 'd_fulladdr',
    'CorpCity' => 'd_city',
    'CorpState' => 'd_state',
    'CorpZip' => 'd_zip',
    'BizPersonEmail' => 'd_email',
    'CorpPhone' => 'd_phone',
    'CorpName' => 'd_busname',
    'CorpDomain' => 'd_domain',
    'LIProfileURL' => 'd_liurl',
];
const CONSUMER_HEADER_MAP = [
    'FirstName' => 'd_first',
    'LastName' => 'd_last',
    'Address' => 'd_fulladdr',
    'City' => 'd_city',
    'State' => 'd_state',
    'Zip' => 'd_zip',
    'EmailAddr' => 'd_email',
    'Phone' => 'd_phone',
    'CorpName' => 'd_busname',
    'CorpDomain' => 'd_domain',
    'LIProfileURL' => 'd_liurl',
];
const CUSTOM_HEADER_MAP = [
    'FirstName' => 'd_first',
    'LastName' => 'd_last',
    'Address' => 'd_fulladdr',
    'City' => 'd_city',
    'State' => 'd_state',
    'Zip' => 'd_zip',
    'Phone1' => 'd_phone',
];
const LISTS = [
    'TS_People_Phones.csv' => [
        'headerMap' => CUSTOM_HEADER_MAP,
    ],
    'Filtered.csv' => [
        'headerMap' => CUSTOM_HEADER_MAP,
    ],
    'Consumer.csv' => [
        'headerMap' => CONSUMER_HEADER_MAP,
    ],
    # for multiPhone test
    'Trestle.csv' => [
        'headerMap' => CUSTOM_HEADER_MAP,
        'recordCount' => 454,
    ],
];

enum Environment {
    case PROD;
    case STG;
}

function runTest(
    string $inputList,
    array $inputParams,
    Environment $env,
)
{
    # vars
    $counter = [
        'curl_errors' => 0,
        'api2b_errors' => 0,
        'returned_P0' => 0,
        'returned_LF' => 0,
        'example_url' => '',
        'matches' => 0,
        'correct_matches' => 0,
    ];
    $urlExample = null;
    # curl_multi stuff
    $mh = curl_multi_init();
    $handles = [];
    $batchSize = 50;

    # read in $inputList
    $fp = fopen('./lists/' . $inputList, 'r');
    $header = fgetcsv($fp);

    while (($rec = fgetcsv($fp)) !== false) {
        # gather params based on selected inputs
        $inputRec = array_combine($header, $rec);
        $params = getParams($inputRec, $inputParams, $inputList);
        $reqUrl = createReqUrl($params, $env);

        if ($urlExample == null) {
            $urlExample = $reqUrl;
            $counter['example_url'] = $urlExample;
        }

        # init curl
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $reqUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_multi_add_handle($mh, $ch);
        $handles[] = [$ch, $inputRec];

        # send batch
        if (count($handles) === $batchSize) {
            // echo "sending batch \n";
            $responses = sendBatchRequest($mh, $handles, $counter);

            foreach($responses as $response) {
                processResponse($response, $counter);
            }
        }

    }
    fclose($fp);

    # send any remaining requests
    if (count($handles) > 0) {
        // echo "sending FINAL batch \n";
        $responses = sendBatchRequest($mh, $handles, $counter);

        foreach($responses as $response) {
            processResponse($response, $counter);
        }
    }


    $curlErrs = $counter['curl_errors'];
    $api2bErrs = $counter['api2b_errors'];

    echo "/******************* Script Finished ******************/ \n";
    echo "There were $curlErrs request errors. \n";
    echo "There were $api2bErrs api2b errors. \n";
    echo "Example Request Url: $urlExample \n";

    # for multiPhone
    echo "Matches: " . $counter['matches'] . "\n";
    echo "Correct Matches: " . $counter['correct_matches'] . "\n";
    $matchRate = ($counter['matches'] / 454) * 100;
    echo "Match Rate $matchRate% \n";
    $accuracyRate = ($counter['correct_matches'] / $counter['matches']) * 100;
    echo "Accuracy Rate: $accuracyRate% \n";

    # write results to csv
    // writeToCSV($counter, $header);
}

// function writeToCSV($counter, $header) {
//     $fp = fopen('./report.csv', 'a+');
//     // $record = [
//     //     $counter['returned_P0'],
//     //     $counter['returned_LF'],
//     //     $counter['example_url'],
//     // ];

//     fputcsv($fp, $header);

//     foreach ($counter['hi'] as $rec) {
//         fputcsv($fp, $rec);
//     }

//     fclose($fp);
// }

function sendBatchRequest($multiHandle, &$handles, &$counter): array
{
    $responses = [];

    # execute multi curl
    $running = null;
    do {
      curl_multi_exec($multiHandle, $running);
    } while ($running);

    # process the responses
    foreach ($handles as [$ch, $inputRec]) {
        $response = curl_multi_getcontent($ch);
        $errNum = curl_errno($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        // $requestTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);

        # store the response
        $responses[] = [json_decode($response, true, 10), $inputRec];

        # curl errors
        if ($errNum || $status != 200) {
            $counter['curl_errors'] += 1;
        }

        # api2b errors
        $jsonRes = json_decode($response);
        if (isset($jsonRes->Versium->errors)) {
            $counter['api2b_errors'] += 1;
        }

        curl_multi_remove_handle($multiHandle, $ch);
    }
    $handles = [];

    return $responses;
}

function processResponse($response, array &$counter)
{
    $result = $response[0]['Versium']['results'][0];
    // var_dump($response);
    // var_dump($result);

    # Multi Phone
    if ($result) {
        # we got a match
        $counter['matches'] += 1;

        #check accuracy
        $phones = [];
        $keys = ['Phone', 'AltPhone0', 'AltPhone1', 'AltPhone2', 'AltPhone3', 'AltPhone4'];

        foreach ($keys as $key) {
            if (isset($result[$key])) {
                array_push($phones, $result[$key]);
            }
        }

        $correct = in_array($response[1]['Phone1'], $phones);
        if ($correct) {
            $counter['correct_matches'] += 1;
        }

        var_dump($phones);
        var_dump($response[1]['Phone1']);

    } else {
        # no match
    }


    // if (isset($result['EmailAddr'])) {
    //     var_dump($result['EmailAddr']);
    // } else {
    //     echo "nada \n";
    // }
}

function getParams(array $inputRec, array $inputParams, string $list): array
{
    $params = [];
    foreach($inputRec as $columnName => $v) {
        if (isset(LISTS[$list]['headerMap'][$columnName])) {
            $queryParam = LISTS[$list]['headerMap'][$columnName];

            if (in_array($queryParam, $inputParams)) {
                $params[$queryParam] = $v;
            }
        }
    }
    return $params;
}

function createReqUrl(array $params = [], $env = Environment::STG): string
{
    $auxParams = [
        // 'cacheBuster' => rand(),
        'cfg_mc' => 'INDIV,S0;HHLD,S0;E0,L0,S0;PINDIV4,S0;LF0,AHN0,CS;LF0,AHN0,Z0,S0;LF0,AS0,CS;LF0,AS0,Z0,S0;DIST,PINDIV,S0', # multi phone MCs
        'cfg_maxrecs' => 1,
        // 'k' => 'nhwuzas5fsfh-phone', # multi phone key
        // 'prodids' => 'email,durttrail,durtwp,cell,cv,basic,cell2,crawlbeewp,telcowp,wparch,auto,crawlbeetrail',
        // 'cfg_output' => 'stats2,basic,email'
    ];

    return $env === Environment::PROD
        ? BASE_URL_PROD . http_build_query(array_merge($params, $auxParams))
        : BASE_URL_STG . http_build_query(array_merge($params, $auxParams));
}




runTest(
    'Trestle.csv',
    [ 'd_first', 'd_last', 'd_fulladdr', 'd_city', 'd_state', 'd_zip', 'd_phone' ],
    Environment::PROD
);
