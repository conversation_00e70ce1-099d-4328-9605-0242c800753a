<?php

// make a POST request (with whatever filters) to endpoint https://api.versium.com/v2/jobs?operation=b2cPeopleListGen

const BASE_URL = 'https://api.versium.com/v2/jobs?operation=b2cPeopleListGen';
const API_KEY = '67049e4a-4d4e-4ad6-b0bd-ef69d3945087';

# setup POST params
$requiredParams = [
    'name' => 'QA_ListGenV2_benchmark_test',
    'project' => 'ListGenV2_Benchmark',
    'project_id' => 76827,
];
$filterParams = [
    'required_fields' => ['address', 'email', 'phone'],
    'd_zip' => ['98007']
];
$finalParams = array_merge($requiredParams, $filterParams);

# setup cUrl
$ch = curl_init();
// curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['X-Versium-Api-Key: 67049e4a-4d4e-4ad6-b0bd-ef69d3945087']);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($finalParams));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_URL, BASE_URL);

# make request
$res = curl_exec($ch);

# handle error state
if (curl_errno($ch)) {
    $err = curl_error($ch);
    echo "There was a cUrl error: $err\n";
    exit;
}

# check status code
$status = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
$data = json_decode($res);

if ($status > 299) {
    $err = $data->versium->errors[0];
    echo "Error from Reach: Status $status: $err \n";
} else {
    # success! grab the state_url and the state
    $state = $data->versium->results->state;
    $stateUrl = $data->versium->results->state_url;
    $jobId = $data->versium->results->id;

    echo "ListGen Created. State: $state\n";
    echo "Status Url: $stateUrl\n";
    echo "Job ID: $jobId\n";

    if ($stateUrl) {
        # ping the stateUrl every 5 sec and check the status
        while ($state !== 'done') {
            sleep(10);
            echo "Checking list status...\n";
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_URL, $stateUrl);
            $statusRes = curl_exec($ch);
            $data = json_decode($statusRes);
            $state = $data->versium->results->state;
            echo "List state: $state\n";
        }
        echo "Job complete.\n";
    }
}

# IF req was a succuss, ping https://api.versium.com/v2/jobs/<job-id> to get status

// start the timer
// ping the endpoint https://api.versium.com/v2/jobs/<job-id> to get the job status (what cadence? every 5 sec?)
// IF job status complete, stop timer; Log time

curl_close($ch);